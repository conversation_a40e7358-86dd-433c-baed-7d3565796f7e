'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, MapPin, ExternalLink, Award } from 'lucide-react';

const Experience = () => {
  const experiences = [
    {
      id: 1,
      title: 'Senior Frontend Developer',
      company: 'Tech Innovations Inc.',
      location: 'San Francisco, CA',
      period: '2023 - Present',
      type: 'Full-time',
      description: 'Leading frontend development for enterprise applications, mentoring junior developers, and implementing modern React architectures.',
      achievements: [
        'Improved application performance by 40% through code optimization',
        'Led migration from legacy codebase to modern React/TypeScript stack',
        'Mentored 5+ junior developers and established coding standards',
        'Implemented automated testing reducing bugs by 60%'
      ],
      technologies: ['React', 'TypeScript', 'Next.js', 'GraphQL', 'AWS'],
      current: true
    },
    {
      id: 2,
      title: 'UI/UX Designer & Frontend Developer',
      company: 'Creative Digital Agency',
      location: 'New York, NY',
      period: '2021 - 2023',
      type: 'Full-time',
      description: 'Designed and developed user interfaces for various client projects, focusing on user experience and modern design principles.',
      achievements: [
        'Designed 20+ responsive web applications and mobile interfaces',
        'Increased user engagement by 35% through UX improvements',
        'Collaborated with cross-functional teams on product strategy',
        'Created comprehensive design systems for multiple clients'
      ],
      technologies: ['Figma', 'React', 'Vue.js', 'Tailwind CSS', 'Adobe Creative Suite'],
      current: false
    },
    {
      id: 3,
      title: 'Frontend Developer',
      company: 'StartupXYZ',
      location: 'Austin, TX',
      period: '2020 - 2021',
      type: 'Full-time',
      description: 'Developed responsive web applications and collaborated with the design team to create intuitive user interfaces.',
      achievements: [
        'Built and maintained 10+ React applications',
        'Implemented responsive designs across multiple devices',
        'Collaborated with backend team to integrate RESTful APIs',
        'Optimized applications for SEO and accessibility'
      ],
      technologies: ['React', 'JavaScript', 'SCSS', 'Node.js', 'MongoDB'],
      current: false
    },
    {
      id: 4,
      title: 'Junior Web Developer',
      company: 'Web Solutions Co.',
      location: 'Remote',
      period: '2019 - 2020',
      type: 'Full-time',
      description: 'Started my professional journey developing websites and learning modern web development practices.',
      achievements: [
        'Developed 15+ client websites using modern frameworks',
        'Learned and implemented responsive design principles',
        'Gained experience with version control and team collaboration',
        'Contributed to open-source projects'
      ],
      technologies: ['HTML', 'CSS', 'JavaScript', 'WordPress', 'PHP'],
      current: false
    }
  ];

  const education = [
    {
      id: 1,
      degree: 'Bachelor of Science in Computer Science',
      school: 'University of Technology',
      location: 'California, USA',
      period: '2015 - 2019',
      description: 'Focused on software engineering, web development, and user interface design.',
      achievements: [
        'Graduated Magna Cum Laude (GPA: 3.8/4.0)',
        'President of Computer Science Club',
        'Winner of Annual Hackathon 2018',
        'Dean\'s List for 6 consecutive semesters'
      ]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section id="experience" className="py-20 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/4 right-1/3 w-64 h-64 bg-accent/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/3 w-64 h-64 bg-accent-secondary/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold font-display mb-4">
            <span className="gradient-accent bg-clip-text text-transparent">
              Experience & Education
            </span>
          </h2>
          <p className="text-lg text-foreground/70 max-w-2xl mx-auto">
            My professional journey and educational background in technology
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-12">
          {/* Experience Timeline */}
          <div className="lg:col-span-2">
            <motion.h3
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-2xl font-semibold font-display mb-8 flex items-center gap-2"
            >
              <Calendar className="w-6 h-6 text-accent" />
              Professional Experience
            </motion.h3>

            <motion.div
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              className="relative"
            >
              {/* Timeline Line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-accent"></div>

              {experiences.map((exp, index) => (
                <motion.div
                  key={exp.id}
                  variants={itemVariants}
                  className="relative flex gap-8 mb-12 last:mb-0"
                >
                  {/* Timeline Dot */}
                  <div className="relative flex-shrink-0">
                    <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
                      exp.current 
                        ? 'bg-gradient-accent animate-pulse-neon' 
                        : 'glass-strong border-2 border-accent/30'
                    }`}>
                      {exp.current ? (
                        <Award className="w-6 h-6 text-white" />
                      ) : (
                        <Calendar className="w-6 h-6 text-accent" />
                      )}
                    </div>
                    {exp.current && (
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                        className="absolute inset-0 rounded-full bg-accent/20 -z-10"
                      />
                    )}
                  </div>

                  {/* Content */}
                  <div className="flex-1 glass-strong rounded-2xl p-6 hover:scale-105 transition-transform duration-300">
                    <div className="flex flex-wrap items-start justify-between gap-4 mb-4">
                      <div>
                        <h4 className="text-xl font-semibold font-display text-accent mb-1">
                          {exp.title}
                        </h4>
                        <p className="text-lg font-medium">{exp.company}</p>
                        <div className="flex items-center gap-4 text-sm text-foreground/70 mt-2">
                          <span className="flex items-center gap-1">
                            <MapPin className="w-4 h-4" />
                            {exp.location}
                          </span>
                          <span className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {exp.period}
                          </span>
                        </div>
                      </div>
                      {exp.current && (
                        <span className="px-3 py-1 bg-gradient-accent text-white text-xs font-semibold rounded-full">
                          Current
                        </span>
                      )}
                    </div>

                    <p className="text-foreground/80 mb-4">{exp.description}</p>

                    {/* Achievements */}
                    <div className="mb-4">
                      <h5 className="font-semibold mb-2">Key Achievements:</h5>
                      <ul className="space-y-1">
                        {exp.achievements.map((achievement, i) => (
                          <li key={i} className="text-sm text-foreground/70 flex items-start gap-2">
                            <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Technologies */}
                    <div className="flex flex-wrap gap-2">
                      {exp.technologies.map((tech) => (
                        <span
                          key={tech}
                          className="px-3 py-1 text-xs font-medium bg-accent/10 text-accent 
                                   rounded-full border border-accent/20"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>

          {/* Education & Certifications */}
          <div>
            <motion.h3
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-2xl font-semibold font-display mb-8 flex items-center gap-2"
            >
              <Award className="w-6 h-6 text-accent" />
              Education
            </motion.h3>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="space-y-6"
            >
              {education.map((edu) => (
                <div key={edu.id} className="glass-strong rounded-2xl p-6">
                  <h4 className="text-lg font-semibold font-display text-accent mb-2">
                    {edu.degree}
                  </h4>
                  <p className="font-medium mb-1">{edu.school}</p>
                  <div className="flex items-center gap-4 text-sm text-foreground/70 mb-3">
                    <span className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      {edu.location}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {edu.period}
                    </span>
                  </div>
                  <p className="text-foreground/80 mb-4">{edu.description}</p>
                  
                  <div>
                    <h5 className="font-semibold mb-2">Achievements:</h5>
                    <ul className="space-y-1">
                      {edu.achievements.map((achievement, i) => (
                        <li key={i} className="text-sm text-foreground/70 flex items-start gap-2">
                          <span className="w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                          {achievement}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}

              {/* Certifications */}
              <div className="glass-strong rounded-2xl p-6">
                <h4 className="text-lg font-semibold font-display text-accent mb-4">
                  Certifications
                </h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">AWS Certified Developer</span>
                    <span className="text-sm text-foreground/70">2023</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Google UX Design Certificate</span>
                    <span className="text-sm text-foreground/70">2022</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="font-medium">React Developer Certification</span>
                    <span className="text-sm text-foreground/70">2021</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Experience;
