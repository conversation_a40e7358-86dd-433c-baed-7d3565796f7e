import { NextResponse } from 'next/server';

// YouTube Data API v3 configuration
const YOUTUBE_API_KEY = process.env.YOUTUBE_API_KEY;
const CHANNEL_ID = process.env.YOUTUBE_CHANNEL_ID;

interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  publishedAt: string;
  viewCount: string;
  likeCount: string;
  duration: string;
  url: string;
}

export async function GET() {
  try {
    // Check if API key and channel ID are configured
    if (!YOUTUBE_API_KEY || !CHANNEL_ID) {
      console.warn('YouTube API key or channel ID not configured, returning mock data');
      return NextResponse.json(getMockVideos());
    }

    // Fetch videos from YouTube API
    const searchResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/search?key=${YOUTUBE_API_KEY}&channelId=${CHANNEL_ID}&part=snippet&order=date&maxResults=6&type=video`
    );

    if (!searchResponse.ok) {
      throw new Error('Failed to fetch videos from YouTube API');
    }

    const searchData = await searchResponse.json();
    const videoIds = searchData.items.map((item: any) => item.id.videoId).join(',');

    // Fetch additional video details
    const detailsResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/videos?key=${YOUTUBE_API_KEY}&id=${videoIds}&part=snippet,statistics,contentDetails`
    );

    if (!detailsResponse.ok) {
      throw new Error('Failed to fetch video details from YouTube API');
    }

    const detailsData = await detailsResponse.json();

    // Transform the data
    const videos: YouTubeVideo[] = detailsData.items.map((item: any) => ({
      id: item.id,
      title: item.snippet.title,
      description: item.snippet.description,
      thumbnail: item.snippet.thumbnails.maxres?.url || item.snippet.thumbnails.high.url,
      publishedAt: item.snippet.publishedAt,
      viewCount: formatNumber(item.statistics.viewCount),
      likeCount: formatNumber(item.statistics.likeCount),
      duration: formatDuration(item.contentDetails.duration),
      url: `https://www.youtube.com/watch?v=${item.id}`
    }));

    return NextResponse.json(videos);
  } catch (error) {
    console.error('Error fetching YouTube videos:', error);
    // Return mock data as fallback
    return NextResponse.json(getMockVideos());
  }
}

function formatNumber(num: string): string {
  const number = parseInt(num);
  if (number >= 1000000) {
    return (number / 1000000).toFixed(1) + 'M';
  } else if (number >= 1000) {
    return (number / 1000).toFixed(1) + 'K';
  }
  return number.toString();
}

function formatDuration(duration: string): string {
  // Convert ISO 8601 duration to MM:SS format
  const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
  if (!match) return '0:00';

  const hours = parseInt(match[1]?.replace('H', '') || '0');
  const minutes = parseInt(match[2]?.replace('M', '') || '0');
  const seconds = parseInt(match[3]?.replace('S', '') || '0');

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

function getMockVideos(): YouTubeVideo[] {
  return [
    {
      id: '1',
      title: 'Building a Modern React Portfolio with Next.js and Framer Motion',
      description: 'Learn how to create a stunning portfolio website using Next.js, TypeScript, and Framer Motion animations.',
      thumbnail: '/youtube-thumb-1.jpg',
      publishedAt: '2024-01-15T10:00:00Z',
      viewCount: '12.5K',
      likeCount: '892',
      duration: '15:32',
      url: 'https://youtube.com/watch?v=example1'
    },
    {
      id: '2',
      title: 'Advanced CSS Animations and Micro-interactions',
      description: 'Dive deep into CSS animations and learn how to create engaging micro-interactions for better UX.',
      thumbnail: '/youtube-thumb-2.jpg',
      publishedAt: '2024-01-08T14:30:00Z',
      viewCount: '8.7K',
      likeCount: '654',
      duration: '22:18',
      url: 'https://youtube.com/watch?v=example2'
    },
    {
      id: '3',
      title: 'TypeScript Best Practices for React Developers',
      description: 'Essential TypeScript patterns and best practices every React developer should know.',
      thumbnail: '/youtube-thumb-3.jpg',
      publishedAt: '2024-01-01T09:15:00Z',
      viewCount: '15.9K',
      likeCount: '1.2K',
      duration: '18:45',
      url: 'https://youtube.com/watch?v=example3'
    },
    {
      id: '4',
      title: 'Creating Responsive Designs with Tailwind CSS',
      description: 'Master responsive design principles using Tailwind CSS utility classes and custom configurations.',
      thumbnail: '/youtube-thumb-4.jpg',
      publishedAt: '2023-12-25T16:45:00Z',
      viewCount: '9.9K',
      likeCount: '743',
      duration: '12:56',
      url: 'https://youtube.com/watch?v=example4'
    },
    {
      id: '5',
      title: 'UI/UX Design Process: From Wireframes to Prototype',
      description: 'Complete walkthrough of my design process from initial wireframes to interactive prototypes.',
      thumbnail: '/youtube-thumb-5.jpg',
      publishedAt: '2023-12-18T11:20:00Z',
      viewCount: '6.5K',
      likeCount: '521',
      duration: '25:12',
      url: 'https://youtube.com/watch?v=example5'
    },
    {
      id: '6',
      title: 'Performance Optimization Techniques for React Apps',
      description: 'Learn advanced techniques to optimize your React applications for better performance and user experience.',
      thumbnail: '/youtube-thumb-6.jpg',
      publishedAt: '2023-12-11T13:00:00Z',
      viewCount: '11.2K',
      likeCount: '876',
      duration: '19:33',
      url: 'https://youtube.com/watch?v=example6'
    }
  ];
}
