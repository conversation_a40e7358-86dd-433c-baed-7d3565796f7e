{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/portfolio/src/contexts/ThemeContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ThemeContext.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ThemeContext.tsx <module evaluation>\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/portfolio/src/contexts/ThemeContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ThemeContext.tsx\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ThemeContext.tsx\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/portfolio/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport \"./globals.css\";\nimport { ThemeProvider } from \"@/contexts/ThemeContext\";\n\nexport const metadata: Metadata = {\n  title: \"Portfolio | Creative Developer & UI/UX Designer\",\n  description: \"Futuristic portfolio showcasing creative front-end development and UI/UX design work with modern technologies like React, Next.js, and TypeScript.\",\n  keywords: [\"portfolio\", \"developer\", \"designer\", \"react\", \"nextjs\", \"typescript\", \"ui/ux\"],\n  authors: [{ name: \"Your Name\" }],\n  viewport: \"width=device-width, initial-scale=1\",\n  robots: \"index, follow\",\n  openGraph: {\n    title: \"Portfolio | Creative Developer & UI/UX Designer\",\n    description: \"Futuristic portfolio showcasing creative front-end development and UI/UX design work\",\n    type: \"website\",\n    locale: \"en_US\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"Portfolio | Creative Developer & UI/UX Designer\",\n    description: \"Futuristic portfolio showcasing creative front-end development and UI/UX design work\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <body className=\"antialiased\">\n        <ThemeProvider>\n          {children}\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAa;QAAa;QAAY;QAAS;QAAU;QAAc;KAAQ;IAC1F,SAAS;QAAC;YAAE,MAAM;QAAY;KAAE;IAChC,UAAU;IACV,QAAQ;IACR,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC,gIAAA,CAAA,gBAAa;0BACX;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/portfolio/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}