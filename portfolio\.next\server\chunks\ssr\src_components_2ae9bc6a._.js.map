{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/portfolio/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useTheme } from '@/contexts/ThemeContext';\nimport { Sun, Moon, Menu, X } from 'lucide-react';\n\nconst Header = () => {\n  const { theme, toggleTheme } = useTheme();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: 'Home', href: '#home' },\n    { name: 'About', href: '#about' },\n    { name: 'Skills', href: '#skills' },\n    { name: 'Projects', href: '#projects' },\n    { name: 'Experience', href: '#experience' },\n    { name: 'YouTube', href: '#youtube' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        scrolled \n          ? 'glass-strong backdrop-blur-md border-b border-glass-border' \n          : 'bg-transparent'\n      }`}\n    >\n      <nav className=\"container mx-auto px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: 0.1 }}\n            className=\"text-2xl font-bold font-display\"\n          >\n            <span className=\"gradient-neon bg-clip-text text-transparent animate-gradient\">\n              Portfolio\n            </span>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item, index) => (\n              <motion.button\n                key={item.name}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.1 + index * 0.1 }}\n                onClick={() => scrollToSection(item.href)}\n                className=\"relative text-foreground hover:text-accent transition-colors duration-300 group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-accent group-hover:w-full transition-all duration-300\"></span>\n              </motion.button>\n            ))}\n          </div>\n\n          {/* Theme Toggle & Mobile Menu */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Theme Toggle */}\n            <motion.button\n              initial={{ opacity: 0, scale: 0 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.3 }}\n              onClick={toggleTheme}\n              className=\"p-2 rounded-full glass hover:glass-strong transition-all duration-300 hover:scale-110\"\n              aria-label=\"Toggle theme\"\n            >\n              <AnimatePresence mode=\"wait\">\n                {theme === 'dark' ? (\n                  <motion.div\n                    key=\"sun\"\n                    initial={{ rotate: -90, opacity: 0 }}\n                    animate={{ rotate: 0, opacity: 1 }}\n                    exit={{ rotate: 90, opacity: 0 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <Sun className=\"w-5 h-5 text-accent\" />\n                  </motion.div>\n                ) : (\n                  <motion.div\n                    key=\"moon\"\n                    initial={{ rotate: 90, opacity: 0 }}\n                    animate={{ rotate: 0, opacity: 1 }}\n                    exit={{ rotate: -90, opacity: 0 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <Moon className=\"w-5 h-5 text-accent\" />\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </motion.button>\n\n            {/* Mobile Menu Button */}\n            <motion.button\n              initial={{ opacity: 0, scale: 0 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.4 }}\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"md:hidden p-2 rounded-full glass hover:glass-strong transition-all duration-300\"\n              aria-label=\"Toggle menu\"\n            >\n              <AnimatePresence mode=\"wait\">\n                {isMenuOpen ? (\n                  <motion.div\n                    key=\"close\"\n                    initial={{ rotate: -90, opacity: 0 }}\n                    animate={{ rotate: 0, opacity: 1 }}\n                    exit={{ rotate: 90, opacity: 0 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <X className=\"w-5 h-5 text-accent\" />\n                  </motion.div>\n                ) : (\n                  <motion.div\n                    key=\"menu\"\n                    initial={{ rotate: 90, opacity: 0 }}\n                    animate={{ rotate: 0, opacity: 1 }}\n                    exit={{ rotate: -90, opacity: 0 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <Menu className=\"w-5 h-5 text-accent\" />\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </motion.button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isMenuOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"md:hidden mt-4 glass-strong rounded-lg overflow-hidden\"\n            >\n              <div className=\"py-4 space-y-2\">\n                {navItems.map((item, index) => (\n                  <motion.button\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    onClick={() => scrollToSection(item.href)}\n                    className=\"block w-full text-left px-4 py-2 text-foreground hover:text-accent hover:bg-glass-bg transition-all duration-300\"\n                  >\n                    {item.name}\n                  </motion.button>\n                ))}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </nav>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAOA,MAAM,SAAS;IACb,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,cAAc;IAChB;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,WAAW,CAAC,4DAA4D,EACtE,WACI,+DACA,kBACJ;kBAEF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCAEV,cAAA,8OAAC;gCAAK,WAAU;0CAA+D;;;;;;;;;;;sCAMjF,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,MAAM,QAAQ;oCAAI;oCACvC,SAAS,IAAM,gBAAgB,KAAK,IAAI;oCACxC,WAAU;;wCAET,KAAK,IAAI;sDACV,8OAAC;4CAAK,WAAU;;;;;;;mCARX,KAAK,IAAI;;;;;;;;;;sCAcpB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;oCAAI;oCACzB,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,yLAAA,CAAA,kBAAe;wCAAC,MAAK;kDACnB,UAAU,uBACT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,QAAQ,CAAC;gDAAI,SAAS;4CAAE;4CACnC,SAAS;gDAAE,QAAQ;gDAAG,SAAS;4CAAE;4CACjC,MAAM;gDAAE,QAAQ;gDAAI,SAAS;4CAAE;4CAC/B,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;2CANX;;;;iEASN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,QAAQ;gDAAI,SAAS;4CAAE;4CAClC,SAAS;gDAAE,QAAQ;gDAAG,SAAS;4CAAE;4CACjC,MAAM;gDAAE,QAAQ,CAAC;gDAAI,SAAS;4CAAE;4CAChC,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;2CANZ;;;;;;;;;;;;;;;8CAaZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;oCAAI;oCACzB,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,yLAAA,CAAA,kBAAe;wCAAC,MAAK;kDACnB,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,QAAQ,CAAC;gDAAI,SAAS;4CAAE;4CACnC,SAAS;gDAAE,QAAQ;gDAAG,SAAS;4CAAE;4CACjC,MAAM;gDAAE,QAAQ;gDAAI,SAAS;4CAAE;4CAC/B,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;2CANT;;;;iEASN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,QAAQ;gDAAI,SAAS;4CAAE;4CAClC,SAAS;gDAAE,QAAQ;gDAAG,SAAS;4CAAE;4CACjC,MAAM;gDAAE,QAAQ,CAAC;gDAAI,SAAS;4CAAE;4CAChC,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;2CANZ;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAehB,8OAAC,yLAAA,CAAA,kBAAe;8BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,SAAS,IAAM,gBAAgB,KAAK,IAAI;oCACxC,WAAU;8CAET,KAAK,IAAI;mCAPL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBlC;uCAEe", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/portfolio/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ArrowDown, Github, Linkedin, Mail, ExternalLink } from 'lucide-react';\nimport Image from 'next/image';\n\nconst Hero = () => {\n  const scrollToProjects = () => {\n    const element = document.querySelector('#projects');\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  const socialLinks = [\n    { icon: Github, href: 'https://github.com', label: 'GitHub' },\n    { icon: Linkedin, href: 'https://linkedin.com', label: 'LinkedIn' },\n    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email' },\n  ];\n\n  return (\n    <section id=\"home\" className=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        {/* Animated gradient background */}\n        <div className=\"absolute inset-0 gradient-primary opacity-10 animate-gradient\"></div>\n        \n        {/* Floating particles */}\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-accent rounded-full opacity-20\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n            }}\n            animate={{\n              y: [0, -30, 0],\n              opacity: [0.2, 0.8, 0.2],\n            }}\n            transition={{\n              duration: 3 + Math.random() * 2,\n              repeat: Infinity,\n              delay: Math.random() * 2,\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"container mx-auto px-6 py-20\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Text Content */}\n          <div className=\"space-y-8\">\n            {/* Greeting */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"space-y-4\"\n            >\n              <motion.p\n                initial={{ opacity: 0, x: -30 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: 0.2, duration: 0.8 }}\n                className=\"text-accent font-medium text-lg\"\n              >\n                Hi there! 👋 I'm\n              </motion.p>\n              \n              <motion.h1\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.4, duration: 0.8 }}\n                className=\"text-5xl md:text-7xl font-bold font-display leading-tight\"\n              >\n                <span className=\"gradient-neon bg-clip-text text-transparent animate-gradient\">\n                  Your Name\n                </span>\n              </motion.h1>\n              \n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.6, duration: 0.8 }}\n                className=\"space-y-2\"\n              >\n                <h2 className=\"text-2xl md:text-3xl font-semibold text-foreground\">\n                  Creative Developer\n                </h2>\n                <h3 className=\"text-xl md:text-2xl text-accent-secondary\">\n                  & UI/UX Designer\n                </h3>\n              </motion.div>\n            </motion.div>\n\n            {/* Description */}\n            <motion.p\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.8, duration: 0.8 }}\n              className=\"text-lg text-foreground/80 leading-relaxed max-w-lg\"\n            >\n              I craft beautiful, functional, and user-centered digital experiences. \n              Passionate about modern web technologies, clean code, and innovative design solutions.\n            </motion.p>\n\n            {/* CTA Buttons */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1, duration: 0.8 }}\n              className=\"flex flex-wrap gap-4\"\n            >\n              <motion.button\n                onClick={scrollToProjects}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"group px-8 py-4 bg-gradient-accent text-white rounded-full font-semibold \n                         hover:shadow-lg hover:shadow-accent/25 transition-all duration-300 \n                         flex items-center gap-2\"\n              >\n                View My Work\n                <ExternalLink className=\"w-4 h-4 group-hover:translate-x-1 transition-transform\" />\n              </motion.button>\n              \n              <motion.a\n                href=\"/resume.pdf\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-8 py-4 glass hover:glass-strong rounded-full font-semibold \n                         border border-accent/30 hover:border-accent/50 transition-all duration-300\"\n              >\n                Download Resume\n              </motion.a>\n            </motion.div>\n\n            {/* Social Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1.2, duration: 0.8 }}\n              className=\"flex gap-4\"\n            >\n              {socialLinks.map((social, index) => (\n                <motion.a\n                  key={social.label}\n                  href={social.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  whileHover={{ scale: 1.1, y: -2 }}\n                  whileTap={{ scale: 0.9 }}\n                  className=\"p-3 glass hover:glass-strong rounded-full \n                           hover:text-accent transition-all duration-300 group\"\n                  aria-label={social.label}\n                >\n                  <social.icon className=\"w-5 h-5 group-hover:scale-110 transition-transform\" />\n                </motion.a>\n              ))}\n            </motion.div>\n          </div>\n\n          {/* Profile Image */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.5, duration: 1 }}\n            className=\"relative flex justify-center lg:justify-end\"\n          >\n            <div className=\"relative\">\n              {/* Glow effect */}\n              <div className=\"absolute inset-0 bg-gradient-accent rounded-full blur-3xl opacity-20 animate-pulse-neon\"></div>\n              \n              {/* Profile image container */}\n              <motion.div\n                animate={{ y: [0, -10, 0] }}\n                transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"relative w-80 h-80 rounded-full overflow-hidden glass-strong border-4 border-accent/30\"\n              >\n                <Image\n                  src=\"/profile-placeholder.jpg\"\n                  alt=\"Profile\"\n                  fill\n                  className=\"object-cover\"\n                  priority\n                />\n              </motion.div>\n              \n              {/* Decorative elements */}\n              <motion.div\n                animate={{ rotate: 360 }}\n                transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n                className=\"absolute -top-4 -right-4 w-8 h-8 border-2 border-accent rounded-full\"\n              />\n              <motion.div\n                animate={{ rotate: -360 }}\n                transition={{ duration: 15, repeat: Infinity, ease: \"linear\" }}\n                className=\"absolute -bottom-4 -left-4 w-6 h-6 bg-accent-secondary rounded-full opacity-60\"\n              />\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 1.5, duration: 0.8 }}\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n        >\n          <motion.button\n            onClick={scrollToProjects}\n            animate={{ y: [0, 10, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"flex flex-col items-center gap-2 text-accent hover:text-accent-secondary transition-colors\"\n            aria-label=\"Scroll to projects\"\n          >\n            <span className=\"text-sm font-medium\">Scroll Down</span>\n            <ArrowDown className=\"w-5 h-5\" />\n          </motion.button>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;AAOA,MAAM,OAAO;IACX,MAAM,mBAAmB;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM,sMAAA,CAAA,SAAM;YAAE,MAAM;YAAsB,OAAO;QAAS;QAC5D;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAwB,OAAO;QAAW;QAClE;YAAE,MAAM,kMAAA,CAAA,OAAI;YAAE,MAAM;YAAiC,OAAO;QAAQ;KACrE;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;oBAGd;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAChC;4BACA,SAAS;gCACP,GAAG;oCAAC;oCAAG,CAAC;oCAAI;iCAAE;gCACd,SAAS;oCAAC;oCAAK;oCAAK;iCAAI;4BAC1B;4BACA,YAAY;gCACV,UAAU,IAAI,KAAK,MAAM,KAAK;gCAC9B,QAAQ;gCACR,OAAO,KAAK,MAAM,KAAK;4BACzB;2BAdK;;;;;;;;;;;0BAmBX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;oDAAK,UAAU;gDAAI;gDACxC,WAAU;0DACX;;;;;;0DAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;oDAAK,UAAU;gDAAI;gDACxC,WAAU;0DAEV,cAAA,8OAAC;oDAAK,WAAU;8DAA+D;;;;;;;;;;;0DAKjF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;oDAAK,UAAU;gDAAI;gDACxC,WAAU;;kEAEV,8OAAC;wDAAG,WAAU;kEAAqD;;;;;;kEAGnE,8OAAC;wDAAG,WAAU;kEAA4C;;;;;;;;;;;;;;;;;;kDAO9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,WAAU;kDACX;;;;;;kDAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;4CAAG,UAAU;wCAAI;wCACtC,WAAU;;0DAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,SAAS;gDACT,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;;oDAGX;kEAEC,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;0DAG1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,MAAK;gDACL,QAAO;gDACP,KAAI;gDACJ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DAEX;;;;;;;;;;;;kDAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,WAAU;kDAET,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDAEP,MAAM,OAAO,IAAI;gDACjB,QAAO;gDACP,KAAI;gDACJ,YAAY;oDAAE,OAAO;oDAAK,GAAG,CAAC;gDAAE;gDAChC,UAAU;oDAAE,OAAO;gDAAI;gDACvB,WAAU;gDAEV,cAAY,OAAO,KAAK;0DAExB,cAAA,8OAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;+CAVlB,OAAO,KAAK;;;;;;;;;;;;;;;;0CAiBzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAE;gCACtC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;;;;;sDAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG;oDAAC;oDAAG,CAAC;oDAAI;iDAAE;4CAAC;4CAC1B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,MAAM;4CAAY;4CAC/D,WAAU;sDAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;gDACV,QAAQ;;;;;;;;;;;sDAKZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,QAAQ;4CAAI;4CACvB,YAAY;gDAAE,UAAU;gDAAI,QAAQ;gDAAU,MAAM;4CAAS;4CAC7D,WAAU;;;;;;sDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,QAAQ,CAAC;4CAAI;4CACxB,YAAY;gDAAE,UAAU;gDAAI,QAAQ;gDAAU,MAAM;4CAAS;4CAC7D,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAOlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;4BACT,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAI;iCAAE;4BAAC;4BACzB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;4BAC5C,WAAU;4BACV,cAAW;;8CAEX,8OAAC;oCAAK,WAAU;8CAAsB;;;;;;8CACtC,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjC;uCAEe", "debugId": null}}, {"offset": {"line": 916, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/portfolio/src/components/Skills.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  Code2, \n  Palette, \n  Database, \n  Globe, \n  Smartphone, \n  Zap,\n  Layers,\n  GitBranch,\n  Monitor,\n  Figma,\n  Cpu,\n  Cloud\n} from 'lucide-react';\n\nconst Skills = () => {\n  const skillCategories = [\n    {\n      title: 'Frontend Development',\n      icon: Code2,\n      skills: [\n        { name: 'React', level: 95, color: 'from-blue-400 to-blue-600' },\n        { name: 'Next.js', level: 90, color: 'from-gray-700 to-black' },\n        { name: 'TypeScript', level: 88, color: 'from-blue-500 to-blue-700' },\n        { name: 'Tailwind CSS', level: 92, color: 'from-cyan-400 to-cyan-600' },\n        { name: 'Framer Motion', level: 85, color: 'from-purple-400 to-purple-600' },\n      ]\n    },\n    {\n      title: 'UI/UX Design',\n      icon: Palette,\n      skills: [\n        { name: 'Figma', level: 90, color: 'from-purple-400 to-pink-400' },\n        { name: 'Adobe XD', level: 85, color: 'from-pink-400 to-red-400' },\n        { name: 'Prototyping', level: 88, color: 'from-green-400 to-green-600' },\n        { name: 'User Research', level: 82, color: 'from-yellow-400 to-orange-400' },\n        { name: 'Design Systems', level: 87, color: 'from-indigo-400 to-indigo-600' },\n      ]\n    },\n    {\n      title: 'Backend & Tools',\n      icon: Database,\n      skills: [\n        { name: 'Node.js', level: 80, color: 'from-green-500 to-green-700' },\n        { name: 'Python', level: 75, color: 'from-yellow-400 to-yellow-600' },\n        { name: 'PostgreSQL', level: 78, color: 'from-blue-600 to-blue-800' },\n        { name: 'Git & GitHub', level: 90, color: 'from-gray-600 to-gray-800' },\n        { name: 'Docker', level: 70, color: 'from-blue-400 to-blue-600' },\n      ]\n    }\n  ];\n\n  const techStack = [\n    { name: 'React', icon: Globe, color: 'text-blue-400' },\n    { name: 'Next.js', icon: Zap, color: 'text-gray-700' },\n    { name: 'TypeScript', icon: Code2, color: 'text-blue-500' },\n    { name: 'Tailwind', icon: Layers, color: 'text-cyan-400' },\n    { name: 'Figma', icon: Figma, color: 'text-purple-400' },\n    { name: 'Git', icon: GitBranch, color: 'text-orange-400' },\n    { name: 'Responsive', icon: Smartphone, color: 'text-green-400' },\n    { name: 'Performance', icon: Cpu, color: 'text-red-400' },\n    { name: 'Cloud', icon: Cloud, color: 'text-blue-300' },\n    { name: 'Modern UI', icon: Monitor, color: 'text-pink-400' },\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6\n      }\n    }\n  };\n\n  return (\n    <section id=\"skills\" className=\"py-20 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/4 left-1/4 w-64 h-64 bg-accent/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-64 h-64 bg-accent-secondary/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-4\">\n            <span className=\"gradient-accent bg-clip-text text-transparent\">\n              Skills & Expertise\n            </span>\n          </h2>\n          <p className=\"text-lg text-foreground/70 max-w-2xl mx-auto\">\n            A comprehensive toolkit for creating exceptional digital experiences\n          </p>\n        </motion.div>\n\n        {/* Skills Categories */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\"\n        >\n          {skillCategories.map((category, categoryIndex) => (\n            <motion.div\n              key={category.title}\n              variants={itemVariants}\n              className=\"glass-strong rounded-2xl p-6 hover:scale-105 transition-transform duration-300\"\n            >\n              <div className=\"flex items-center gap-3 mb-6\">\n                <div className=\"p-3 bg-gradient-accent rounded-lg\">\n                  <category.icon className=\"w-6 h-6 text-white\" />\n                </div>\n                <h3 className=\"text-xl font-semibold font-display\">{category.title}</h3>\n              </div>\n\n              <div className=\"space-y-4\">\n                {category.skills.map((skill, skillIndex) => (\n                  <div key={skill.name} className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"font-medium\">{skill.name}</span>\n                      <span className=\"text-sm text-accent\">{skill.level}%</span>\n                    </div>\n                    <div className=\"w-full bg-muted rounded-full h-2 overflow-hidden\">\n                      <motion.div\n                        initial={{ width: 0 }}\n                        whileInView={{ width: `${skill.level}%` }}\n                        viewport={{ once: true }}\n                        transition={{ \n                          duration: 1, \n                          delay: categoryIndex * 0.2 + skillIndex * 0.1 \n                        }}\n                        className={`h-full bg-gradient-to-r ${skill.color} rounded-full`}\n                      />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Tech Stack Icons */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          className=\"text-center\"\n        >\n          <h3 className=\"text-2xl font-semibold font-display mb-8\">\n            Technologies I Work With\n          </h3>\n          \n          <div className=\"flex flex-wrap justify-center gap-6\">\n            {techStack.map((tech, index) => (\n              <motion.div\n                key={tech.name}\n                initial={{ opacity: 0, scale: 0 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                viewport={{ once: true }}\n                transition={{ \n                  duration: 0.5, \n                  delay: index * 0.1,\n                  type: \"spring\",\n                  stiffness: 100\n                }}\n                whileHover={{ \n                  scale: 1.1, \n                  y: -5,\n                  transition: { duration: 0.2 }\n                }}\n                className=\"group flex flex-col items-center gap-2 p-4 glass hover:glass-strong \n                         rounded-xl transition-all duration-300 cursor-pointer\"\n              >\n                <tech.icon className={`w-8 h-8 ${tech.color} group-hover:scale-110 transition-transform`} />\n                <span className=\"text-sm font-medium group-hover:text-accent transition-colors\">\n                  {tech.name}\n                </span>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Skills;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAmBA,MAAM,SAAS;IACb,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,MAAM,0MAAA,CAAA,QAAK;YACX,QAAQ;gBACN;oBAAE,MAAM;oBAAS,OAAO;oBAAI,OAAO;gBAA4B;gBAC/D;oBAAE,MAAM;oBAAW,OAAO;oBAAI,OAAO;gBAAyB;gBAC9D;oBAAE,MAAM;oBAAc,OAAO;oBAAI,OAAO;gBAA4B;gBACpE;oBAAE,MAAM;oBAAgB,OAAO;oBAAI,OAAO;gBAA4B;gBACtE;oBAAE,MAAM;oBAAiB,OAAO;oBAAI,OAAO;gBAAgC;aAC5E;QACH;QACA;YACE,OAAO;YACP,MAAM,wMAAA,CAAA,UAAO;YACb,QAAQ;gBACN;oBAAE,MAAM;oBAAS,OAAO;oBAAI,OAAO;gBAA8B;gBACjE;oBAAE,MAAM;oBAAY,OAAO;oBAAI,OAAO;gBAA2B;gBACjE;oBAAE,MAAM;oBAAe,OAAO;oBAAI,OAAO;gBAA8B;gBACvE;oBAAE,MAAM;oBAAiB,OAAO;oBAAI,OAAO;gBAAgC;gBAC3E;oBAAE,MAAM;oBAAkB,OAAO;oBAAI,OAAO;gBAAgC;aAC7E;QACH;QACA;YACE,OAAO;YACP,MAAM,0MAAA,CAAA,WAAQ;YACd,QAAQ;gBACN;oBAAE,MAAM;oBAAW,OAAO;oBAAI,OAAO;gBAA8B;gBACnE;oBAAE,MAAM;oBAAU,OAAO;oBAAI,OAAO;gBAAgC;gBACpE;oBAAE,MAAM;oBAAc,OAAO;oBAAI,OAAO;gBAA4B;gBACpE;oBAAE,MAAM;oBAAgB,OAAO;oBAAI,OAAO;gBAA4B;gBACtE;oBAAE,MAAM;oBAAU,OAAO;oBAAI,OAAO;gBAA4B;aACjE;QACH;KACD;IAED,MAAM,YAAY;QAChB;YAAE,MAAM;YAAS,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO;QAAgB;QACrD;YAAE,MAAM;YAAW,MAAM,gMAAA,CAAA,MAAG;YAAE,OAAO;QAAgB;QACrD;YAAE,MAAM;YAAc,MAAM,0MAAA,CAAA,QAAK;YAAE,OAAO;QAAgB;QAC1D;YAAE,MAAM;YAAY,MAAM,sMAAA,CAAA,SAAM;YAAE,OAAO;QAAgB;QACzD;YAAE,MAAM;YAAS,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO;QAAkB;QACvD;YAAE,MAAM;YAAO,MAAM,gNAAA,CAAA,YAAS;YAAE,OAAO;QAAkB;QACzD;YAAE,MAAM;YAAc,MAAM,8MAAA,CAAA,aAAU;YAAE,OAAO;QAAiB;QAChE;YAAE,MAAM;YAAe,MAAM,gMAAA,CAAA,MAAG;YAAE,OAAO;QAAe;QACxD;YAAE,MAAM;YAAS,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO;QAAgB;QACrD;YAAE,MAAM;YAAa,MAAM,wMAAA,CAAA,UAAO;YAAE,OAAO;QAAgB;KAC5D;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;;0BAE7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;0CAIlE,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAM9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAET,gBAAgB,GAAG,CAAC,CAAC,UAAU,8BAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,SAAS,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAE3B,8OAAC;gDAAG,WAAU;0DAAsC,SAAS,KAAK;;;;;;;;;;;;kDAGpE,8OAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC3B,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAe,MAAM,IAAI;;;;;;0EACzC,8OAAC;gEAAK,WAAU;;oEAAuB,MAAM,KAAK;oEAAC;;;;;;;;;;;;;kEAErD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,OAAO;4DAAE;4DACpB,aAAa;gEAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;4DAAC;4DACxC,UAAU;gEAAE,MAAM;4DAAK;4DACvB,YAAY;gEACV,UAAU;gEACV,OAAO,gBAAgB,MAAM,aAAa;4DAC5C;4DACA,WAAW,CAAC,wBAAwB,EAAE,MAAM,KAAK,CAAC,aAAa,CAAC;;;;;;;;;;;;+CAd5D,MAAM,IAAI;;;;;;;;;;;+BAbnB,SAAS,KAAK;;;;;;;;;;kCAsCzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CACV,UAAU;4CACV,OAAO,QAAQ;4CACf,MAAM;4CACN,WAAW;wCACb;wCACA,YAAY;4CACV,OAAO;4CACP,GAAG,CAAC;4CACJ,YAAY;gDAAE,UAAU;4CAAI;wCAC9B;wCACA,WAAU;;0DAGV,8OAAC,KAAK,IAAI;gDAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,2CAA2C,CAAC;;;;;;0DACxF,8OAAC;gDAAK,WAAU;0DACb,KAAK,IAAI;;;;;;;uCApBP,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6B9B;uCAEe", "debugId": null}}, {"offset": {"line": 1417, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/portfolio/src/components/Projects.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ExternalLink, Github, Eye, Filter } from 'lucide-react';\nimport Image from 'next/image';\n\nconst Projects = () => {\n  const [filter, setFilter] = useState('all');\n\n  const projects = [\n    {\n      id: 1,\n      title: 'E-Commerce Platform',\n      description: 'A modern, responsive e-commerce platform built with Next.js, featuring real-time inventory, payment integration, and admin dashboard.',\n      image: '/project-1.jpg',\n      tags: ['Next.js', 'TypeScript', 'Stripe', 'Prisma'],\n      category: 'fullstack',\n      liveUrl: 'https://example.com',\n      githubUrl: 'https://github.com/example',\n      featured: true\n    },\n    {\n      id: 2,\n      title: 'Task Management App',\n      description: 'Collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',\n      image: '/project-2.jpg',\n      tags: ['React', 'Node.js', 'Socket.io', 'MongoDB'],\n      category: 'fullstack',\n      liveUrl: 'https://example.com',\n      githubUrl: 'https://github.com/example',\n      featured: true\n    },\n    {\n      id: 3,\n      title: 'Design System',\n      description: 'Comprehensive design system with reusable components, documentation, and Figma integration for consistent UI/UX.',\n      image: '/project-3.jpg',\n      tags: ['Figma', 'Storybook', 'React', 'Tailwind'],\n      category: 'design',\n      liveUrl: 'https://example.com',\n      githubUrl: 'https://github.com/example',\n      featured: false\n    },\n    {\n      id: 4,\n      title: 'Weather Dashboard',\n      description: 'Beautiful weather dashboard with interactive maps, forecasts, and location-based weather data visualization.',\n      image: '/project-4.jpg',\n      tags: ['React', 'D3.js', 'Weather API', 'Charts'],\n      category: 'frontend',\n      liveUrl: 'https://example.com',\n      githubUrl: 'https://github.com/example',\n      featured: false\n    },\n    {\n      id: 5,\n      title: 'Mobile Banking App',\n      description: 'Secure mobile banking interface design with intuitive UX, biometric authentication, and accessibility features.',\n      image: '/project-5.jpg',\n      tags: ['Figma', 'Prototyping', 'User Research', 'Mobile UI'],\n      category: 'design',\n      liveUrl: 'https://example.com',\n      githubUrl: 'https://github.com/example',\n      featured: true\n    },\n    {\n      id: 6,\n      title: 'Portfolio Website',\n      description: 'This very portfolio website showcasing modern web development techniques with animations and responsive design.',\n      image: '/project-6.jpg',\n      tags: ['Next.js', 'Framer Motion', 'Tailwind', 'TypeScript'],\n      category: 'frontend',\n      liveUrl: 'https://example.com',\n      githubUrl: 'https://github.com/example',\n      featured: false\n    }\n  ];\n\n  const categories = [\n    { id: 'all', name: 'All Projects' },\n    { id: 'fullstack', name: 'Full Stack' },\n    { id: 'frontend', name: 'Frontend' },\n    { id: 'design', name: 'UI/UX Design' }\n  ];\n\n  const filteredProjects = filter === 'all' \n    ? projects \n    : projects.filter(project => project.category === filter);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6\n      }\n    }\n  };\n\n  return (\n    <section id=\"projects\" className=\"py-20 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/3 right-1/4 w-96 h-96 bg-accent/5 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/3 left-1/4 w-96 h-96 bg-accent-secondary/5 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-4\">\n            <span className=\"gradient-accent bg-clip-text text-transparent\">\n              Featured Projects\n            </span>\n          </h2>\n          <p className=\"text-lg text-foreground/70 max-w-2xl mx-auto\">\n            A showcase of my recent work in web development and UI/UX design\n          </p>\n        </motion.div>\n\n        {/* Filter Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"flex flex-wrap justify-center gap-4 mb-12\"\n        >\n          {categories.map((category) => (\n            <motion.button\n              key={category.id}\n              onClick={() => setFilter(category.id)}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center gap-2 ${\n                filter === category.id\n                  ? 'bg-gradient-accent text-white shadow-lg'\n                  : 'glass hover:glass-strong text-foreground hover:text-accent'\n              }`}\n            >\n              <Filter className=\"w-4 h-4\" />\n              {category.name}\n            </motion.button>\n          ))}\n        </motion.div>\n\n        {/* Projects Grid */}\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={filter}\n            variants={containerVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            exit=\"hidden\"\n            className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n          >\n            {filteredProjects.map((project) => (\n              <motion.div\n                key={project.id}\n                variants={itemVariants}\n                layout\n                className={`group relative glass-strong rounded-2xl overflow-hidden hover:scale-105 \n                          transition-all duration-500 ${project.featured ? 'md:col-span-2 lg:col-span-1' : ''}`}\n              >\n                {/* Project Image */}\n                <div className=\"relative h-48 overflow-hidden\">\n                  <Image\n                    src={project.image}\n                    alt={project.title}\n                    fill\n                    className=\"object-cover group-hover:scale-110 transition-transform duration-500\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 \n                                group-hover:opacity-100 transition-opacity duration-300\" />\n                  \n                  {/* Overlay Actions */}\n                  <div className=\"absolute inset-0 flex items-center justify-center gap-4 opacity-0 \n                                group-hover:opacity-100 transition-opacity duration-300\">\n                    <motion.a\n                      href={project.liveUrl}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                      className=\"p-3 bg-white/20 backdrop-blur-sm rounded-full hover:bg-accent/80 \n                               transition-colors duration-300\"\n                      aria-label=\"View live project\"\n                    >\n                      <ExternalLink className=\"w-5 h-5 text-white\" />\n                    </motion.a>\n                    <motion.a\n                      href={project.githubUrl}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                      className=\"p-3 bg-white/20 backdrop-blur-sm rounded-full hover:bg-accent/80 \n                               transition-colors duration-300\"\n                      aria-label=\"View source code\"\n                    >\n                      <Github className=\"w-5 h-5 text-white\" />\n                    </motion.a>\n                  </div>\n\n                  {/* Featured Badge */}\n                  {project.featured && (\n                    <div className=\"absolute top-4 left-4 px-3 py-1 bg-gradient-accent text-white \n                                  text-xs font-semibold rounded-full\">\n                      Featured\n                    </div>\n                  )}\n                </div>\n\n                {/* Project Content */}\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-semibold font-display mb-2 group-hover:text-accent \n                               transition-colors duration-300\">\n                    {project.title}\n                  </h3>\n                  <p className=\"text-foreground/70 mb-4 line-clamp-3\">\n                    {project.description}\n                  </p>\n\n                  {/* Tags */}\n                  <div className=\"flex flex-wrap gap-2 mb-4\">\n                    {project.tags.map((tag) => (\n                      <span\n                        key={tag}\n                        className=\"px-3 py-1 text-xs font-medium bg-accent/10 text-accent \n                                 rounded-full border border-accent/20\"\n                      >\n                        {tag}\n                      </span>\n                    ))}\n                  </div>\n\n                  {/* Action Links */}\n                  <div className=\"flex gap-4\">\n                    <a\n                      href={project.liveUrl}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"flex items-center gap-2 text-accent hover:text-accent-secondary \n                               transition-colors duration-300 font-medium\"\n                    >\n                      <Eye className=\"w-4 h-4\" />\n                      Live Demo\n                    </a>\n                    <a\n                      href={project.githubUrl}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"flex items-center gap-2 text-foreground/70 hover:text-accent \n                               transition-colors duration-300 font-medium\"\n                    >\n                      <Github className=\"w-4 h-4\" />\n                      Code\n                    </a>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </AnimatePresence>\n\n        {/* View More Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"text-center mt-12\"\n        >\n          <motion.a\n            href=\"https://github.com/yourusername\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"inline-flex items-center gap-2 px-8 py-4 glass hover:glass-strong \n                     rounded-full font-semibold border border-accent/30 hover:border-accent/50 \n                     transition-all duration-300\"\n          >\n            <Github className=\"w-5 h-5\" />\n            View All Projects on GitHub\n          </motion.a>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Projects;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,WAAW;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,MAAM;gBAAC;gBAAW;gBAAc;gBAAU;aAAS;YACnD,UAAU;YACV,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,MAAM;gBAAC;gBAAS;gBAAW;gBAAa;aAAU;YAClD,UAAU;YACV,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,IAAI;YAC<PERSON>,OAAO;YACP,aAAa;YACb,OAAO;YACP,MAAM;gBAAC;gBAAS;gBAAa;gBAAS;aAAW;YACjD,UAAU;YACV,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,MAAM;gBAAC;gBAAS;gBAAS;gBAAe;aAAS;YACjD,UAAU;YACV,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,MAAM;gBAAC;gBAAS;gBAAe;gBAAiB;aAAY;YAC5D,UAAU;YACV,SAAS;YACT,WAAW;YACX,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,MAAM;gBAAC;gBAAW;gBAAiB;gBAAY;aAAa;YAC5D,UAAU;YACV,SAAS;YACT,WAAW;YACX,UAAU;QACZ;KACD;IAED,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;QAAe;QAClC;YAAE,IAAI;YAAa,MAAM;QAAa;QACtC;YAAE,IAAI;YAAY,MAAM;QAAW;QACnC;YAAE,IAAI;YAAU,MAAM;QAAe;KACtC;IAED,MAAM,mBAAmB,WAAW,QAChC,WACA,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEpD,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;;0BAE/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;0CAIlE,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAM9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAET,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS,IAAM,UAAU,SAAS,EAAE;gCACpC,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAW,CAAC,uFAAuF,EACjG,WAAW,SAAS,EAAE,GAClB,4CACA,8DACJ;;kDAEF,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACjB,SAAS,IAAI;;+BAXT,SAAS,EAAE;;;;;;;;;;kCAiBtB,8OAAC,yLAAA,CAAA,kBAAe;wBAAC,MAAK;kCACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,MAAK;4BACL,WAAU;sCAET,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,MAAM;oCACN,WAAW,CAAC;sDAC0B,EAAE,QAAQ,QAAQ,GAAG,gCAAgC,IAAI;;sDAG/F,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,QAAQ,KAAK;oDAClB,KAAK,QAAQ,KAAK;oDAClB,IAAI;oDACJ,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;8DAIf,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4DACP,MAAM,QAAQ,OAAO;4DACrB,QAAO;4DACP,KAAI;4DACJ,YAAY;gEAAE,OAAO;4DAAI;4DACzB,UAAU;gEAAE,OAAO;4DAAI;4DACvB,WAAU;4DAEV,cAAW;sEAEX,cAAA,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4DACP,MAAM,QAAQ,SAAS;4DACvB,QAAO;4DACP,KAAI;4DACJ,YAAY;gEAAE,OAAO;4DAAI;4DACzB,UAAU;gEAAE,OAAO;4DAAI;4DACvB,WAAU;4DAEV,cAAW;sEAEX,cAAA,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;gDAKrB,QAAQ,QAAQ,kBACf,8OAAC;oDAAI,WAAU;8DACmC;;;;;;;;;;;;sDAOtD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAEX,QAAQ,KAAK;;;;;;8DAEhB,8OAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;8DAItB,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,oBACjB,8OAAC;4DAEC,WAAU;sEAGT;2DAJI;;;;;;;;;;8DAUX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAM,QAAQ,OAAO;4DACrB,QAAO;4DACP,KAAI;4DACJ,WAAU;;8EAGV,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAG7B,8OAAC;4DACC,MAAM,QAAQ,SAAS;4DACvB,QAAO;4DACP,KAAI;4DACJ,WAAU;;8EAGV,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;mCAjG/B,QAAQ,EAAE;;;;;2BATd;;;;;;;;;;kCAqHT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,MAAK;4BACL,QAAO;4BACP,KAAI;4BACJ,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;;8CAIV,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAO1C;uCAEe", "debugId": null}}, {"offset": {"line": 1978, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/portfolio/src/components/Experience.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Calendar, MapPin, ExternalLink, Award } from 'lucide-react';\n\nconst Experience = () => {\n  const experiences = [\n    {\n      id: 1,\n      title: 'Senior Frontend Developer',\n      company: 'Tech Innovations Inc.',\n      location: 'San Francisco, CA',\n      period: '2023 - Present',\n      type: 'Full-time',\n      description: 'Leading frontend development for enterprise applications, mentoring junior developers, and implementing modern React architectures.',\n      achievements: [\n        'Improved application performance by 40% through code optimization',\n        'Led migration from legacy codebase to modern React/TypeScript stack',\n        'Mentored 5+ junior developers and established coding standards',\n        'Implemented automated testing reducing bugs by 60%'\n      ],\n      technologies: ['React', 'TypeScript', 'Next.js', 'GraphQL', 'AWS'],\n      current: true\n    },\n    {\n      id: 2,\n      title: 'UI/UX Designer & Frontend Developer',\n      company: 'Creative Digital Agency',\n      location: 'New York, NY',\n      period: '2021 - 2023',\n      type: 'Full-time',\n      description: 'Designed and developed user interfaces for various client projects, focusing on user experience and modern design principles.',\n      achievements: [\n        'Designed 20+ responsive web applications and mobile interfaces',\n        'Increased user engagement by 35% through UX improvements',\n        'Collaborated with cross-functional teams on product strategy',\n        'Created comprehensive design systems for multiple clients'\n      ],\n      technologies: ['Figma', 'React', 'Vue.js', 'Tailwind CSS', 'Adobe Creative Suite'],\n      current: false\n    },\n    {\n      id: 3,\n      title: 'Frontend Developer',\n      company: 'StartupXYZ',\n      location: 'Austin, TX',\n      period: '2020 - 2021',\n      type: 'Full-time',\n      description: 'Developed responsive web applications and collaborated with the design team to create intuitive user interfaces.',\n      achievements: [\n        'Built and maintained 10+ React applications',\n        'Implemented responsive designs across multiple devices',\n        'Collaborated with backend team to integrate RESTful APIs',\n        'Optimized applications for SEO and accessibility'\n      ],\n      technologies: ['React', 'JavaScript', 'SCSS', 'Node.js', 'MongoDB'],\n      current: false\n    },\n    {\n      id: 4,\n      title: 'Junior Web Developer',\n      company: 'Web Solutions Co.',\n      location: 'Remote',\n      period: '2019 - 2020',\n      type: 'Full-time',\n      description: 'Started my professional journey developing websites and learning modern web development practices.',\n      achievements: [\n        'Developed 15+ client websites using modern frameworks',\n        'Learned and implemented responsive design principles',\n        'Gained experience with version control and team collaboration',\n        'Contributed to open-source projects'\n      ],\n      technologies: ['HTML', 'CSS', 'JavaScript', 'WordPress', 'PHP'],\n      current: false\n    }\n  ];\n\n  const education = [\n    {\n      id: 1,\n      degree: 'Bachelor of Science in Computer Science',\n      school: 'University of Technology',\n      location: 'California, USA',\n      period: '2015 - 2019',\n      description: 'Focused on software engineering, web development, and user interface design.',\n      achievements: [\n        'Graduated Magna Cum Laude (GPA: 3.8/4.0)',\n        'President of Computer Science Club',\n        'Winner of Annual Hackathon 2018',\n        'Dean\\'s List for 6 consecutive semesters'\n      ]\n    }\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, x: -20 },\n    visible: {\n      opacity: 1,\n      x: 0,\n      transition: {\n        duration: 0.6\n      }\n    }\n  };\n\n  return (\n    <section id=\"experience\" className=\"py-20 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/4 right-1/3 w-64 h-64 bg-accent/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/4 left-1/3 w-64 h-64 bg-accent-secondary/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-4\">\n            <span className=\"gradient-accent bg-clip-text text-transparent\">\n              Experience & Education\n            </span>\n          </h2>\n          <p className=\"text-lg text-foreground/70 max-w-2xl mx-auto\">\n            My professional journey and educational background in technology\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-3 gap-12\">\n          {/* Experience Timeline */}\n          <div className=\"lg:col-span-2\">\n            <motion.h3\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              className=\"text-2xl font-semibold font-display mb-8 flex items-center gap-2\"\n            >\n              <Calendar className=\"w-6 h-6 text-accent\" />\n              Professional Experience\n            </motion.h3>\n\n            <motion.div\n              variants={containerVariants}\n              initial=\"hidden\"\n              whileInView=\"visible\"\n              viewport={{ once: true }}\n              className=\"relative\"\n            >\n              {/* Timeline Line */}\n              <div className=\"absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-accent\"></div>\n\n              {experiences.map((exp, index) => (\n                <motion.div\n                  key={exp.id}\n                  variants={itemVariants}\n                  className=\"relative flex gap-8 mb-12 last:mb-0\"\n                >\n                  {/* Timeline Dot */}\n                  <div className=\"relative flex-shrink-0\">\n                    <div className={`w-16 h-16 rounded-full flex items-center justify-center ${\n                      exp.current \n                        ? 'bg-gradient-accent animate-pulse-neon' \n                        : 'glass-strong border-2 border-accent/30'\n                    }`}>\n                      {exp.current ? (\n                        <Award className=\"w-6 h-6 text-white\" />\n                      ) : (\n                        <Calendar className=\"w-6 h-6 text-accent\" />\n                      )}\n                    </div>\n                    {exp.current && (\n                      <motion.div\n                        animate={{ scale: [1, 1.2, 1] }}\n                        transition={{ duration: 2, repeat: Infinity }}\n                        className=\"absolute inset-0 rounded-full bg-accent/20 -z-10\"\n                      />\n                    )}\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"flex-1 glass-strong rounded-2xl p-6 hover:scale-105 transition-transform duration-300\">\n                    <div className=\"flex flex-wrap items-start justify-between gap-4 mb-4\">\n                      <div>\n                        <h4 className=\"text-xl font-semibold font-display text-accent mb-1\">\n                          {exp.title}\n                        </h4>\n                        <p className=\"text-lg font-medium\">{exp.company}</p>\n                        <div className=\"flex items-center gap-4 text-sm text-foreground/70 mt-2\">\n                          <span className=\"flex items-center gap-1\">\n                            <MapPin className=\"w-4 h-4\" />\n                            {exp.location}\n                          </span>\n                          <span className=\"flex items-center gap-1\">\n                            <Calendar className=\"w-4 h-4\" />\n                            {exp.period}\n                          </span>\n                        </div>\n                      </div>\n                      {exp.current && (\n                        <span className=\"px-3 py-1 bg-gradient-accent text-white text-xs font-semibold rounded-full\">\n                          Current\n                        </span>\n                      )}\n                    </div>\n\n                    <p className=\"text-foreground/80 mb-4\">{exp.description}</p>\n\n                    {/* Achievements */}\n                    <div className=\"mb-4\">\n                      <h5 className=\"font-semibold mb-2\">Key Achievements:</h5>\n                      <ul className=\"space-y-1\">\n                        {exp.achievements.map((achievement, i) => (\n                          <li key={i} className=\"text-sm text-foreground/70 flex items-start gap-2\">\n                            <span className=\"w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0\"></span>\n                            {achievement}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n\n                    {/* Technologies */}\n                    <div className=\"flex flex-wrap gap-2\">\n                      {exp.technologies.map((tech) => (\n                        <span\n                          key={tech}\n                          className=\"px-3 py-1 text-xs font-medium bg-accent/10 text-accent \n                                   rounded-full border border-accent/20\"\n                        >\n                          {tech}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </motion.div>\n          </div>\n\n          {/* Education & Certifications */}\n          <div>\n            <motion.h3\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              className=\"text-2xl font-semibold font-display mb-8 flex items-center gap-2\"\n            >\n              <Award className=\"w-6 h-6 text-accent\" />\n              Education\n            </motion.h3>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ delay: 0.2 }}\n              className=\"space-y-6\"\n            >\n              {education.map((edu) => (\n                <div key={edu.id} className=\"glass-strong rounded-2xl p-6\">\n                  <h4 className=\"text-lg font-semibold font-display text-accent mb-2\">\n                    {edu.degree}\n                  </h4>\n                  <p className=\"font-medium mb-1\">{edu.school}</p>\n                  <div className=\"flex items-center gap-4 text-sm text-foreground/70 mb-3\">\n                    <span className=\"flex items-center gap-1\">\n                      <MapPin className=\"w-4 h-4\" />\n                      {edu.location}\n                    </span>\n                    <span className=\"flex items-center gap-1\">\n                      <Calendar className=\"w-4 h-4\" />\n                      {edu.period}\n                    </span>\n                  </div>\n                  <p className=\"text-foreground/80 mb-4\">{edu.description}</p>\n                  \n                  <div>\n                    <h5 className=\"font-semibold mb-2\">Achievements:</h5>\n                    <ul className=\"space-y-1\">\n                      {edu.achievements.map((achievement, i) => (\n                        <li key={i} className=\"text-sm text-foreground/70 flex items-start gap-2\">\n                          <span className=\"w-1.5 h-1.5 bg-accent rounded-full mt-2 flex-shrink-0\"></span>\n                          {achievement}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              ))}\n\n              {/* Certifications */}\n              <div className=\"glass-strong rounded-2xl p-6\">\n                <h4 className=\"text-lg font-semibold font-display text-accent mb-4\">\n                  Certifications\n                </h4>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"font-medium\">AWS Certified Developer</span>\n                    <span className=\"text-sm text-foreground/70\">2023</span>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"font-medium\">Google UX Design Certificate</span>\n                    <span className=\"text-sm text-foreground/70\">2022</span>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"font-medium\">React Developer Certification</span>\n                    <span className=\"text-sm text-foreground/70\">2021</span>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Experience;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAJA;;;;AAMA,MAAM,aAAa;IACjB,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,MAAM;YACN,aAAa;YACb,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBAAC;gBAAS;gBAAc;gBAAW;gBAAW;aAAM;YAClE,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,MAAM;YACN,aAAa;YACb,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBAAC;gBAAS;gBAAS;gBAAU;gBAAgB;aAAuB;YAClF,SAAS;QACX;QACA;YACE,IAAI;YAC<PERSON>,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,MAAM;YACN,aAAa;YACb,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBAAC;gBAAS;gBAAc;gBAAQ;gBAAW;aAAU;YACnE,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,MAAM;YACN,aAAa;YACb,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBAAC;gBAAQ;gBAAO;gBAAc;gBAAa;aAAM;YAC/D,SAAS;QACX;KACD;IAED,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,aAAa;YACb,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;QACH;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAa,WAAU;;0BAEjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;0CAIlE,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAK9D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAI9C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,UAAU;wCACV,SAAQ;wCACR,aAAY;wCACZ,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAGV,8OAAC;gDAAI,WAAU;;;;;;4CAEd,YAAY,GAAG,CAAC,CAAC,KAAK,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,UAAU;oDACV,WAAU;;sEAGV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAW,CAAC,wDAAwD,EACvE,IAAI,OAAO,GACP,0CACA,0CACJ;8EACC,IAAI,OAAO,iBACV,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;6FAEjB,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;gEAGvB,IAAI,OAAO,kBACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,SAAS;wEAAE,OAAO;4EAAC;4EAAG;4EAAK;yEAAE;oEAAC;oEAC9B,YAAY;wEAAE,UAAU;wEAAG,QAAQ;oEAAS;oEAC5C,WAAU;;;;;;;;;;;;sEAMhB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;8FACC,8OAAC;oFAAG,WAAU;8FACX,IAAI,KAAK;;;;;;8FAEZ,8OAAC;oFAAE,WAAU;8FAAuB,IAAI,OAAO;;;;;;8FAC/C,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;;8GACd,8OAAC,0MAAA,CAAA,SAAM;oGAAC,WAAU;;;;;;gGACjB,IAAI,QAAQ;;;;;;;sGAEf,8OAAC;4FAAK,WAAU;;8GACd,8OAAC,0MAAA,CAAA,WAAQ;oGAAC,WAAU;;;;;;gGACnB,IAAI,MAAM;;;;;;;;;;;;;;;;;;;wEAIhB,IAAI,OAAO,kBACV,8OAAC;4EAAK,WAAU;sFAA6E;;;;;;;;;;;;8EAMjG,8OAAC;oEAAE,WAAU;8EAA2B,IAAI,WAAW;;;;;;8EAGvD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAqB;;;;;;sFACnC,8OAAC;4EAAG,WAAU;sFACX,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,kBAClC,8OAAC;oFAAW,WAAU;;sGACpB,8OAAC;4FAAK,WAAU;;;;;;wFACf;;mFAFM;;;;;;;;;;;;;;;;8EASf,8OAAC;oEAAI,WAAU;8EACZ,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC;4EAEC,WAAU;sFAGT;2EAJI;;;;;;;;;;;;;;;;;mDAvER,IAAI,EAAE;;;;;;;;;;;;;;;;;0CAsFnB,8OAAC;;kDACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAI3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;;4CAET,UAAU,GAAG,CAAC,CAAC,oBACd,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;4DAAG,WAAU;sEACX,IAAI,MAAM;;;;;;sEAEb,8OAAC;4DAAE,WAAU;sEAAoB,IAAI,MAAM;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;;sFACd,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,IAAI,QAAQ;;;;;;;8EAEf,8OAAC;oEAAK,WAAU;;sFACd,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEACnB,IAAI,MAAM;;;;;;;;;;;;;sEAGf,8OAAC;4DAAE,WAAU;sEAA2B,IAAI,WAAW;;;;;;sEAEvD,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAqB;;;;;;8EACnC,8OAAC;oEAAG,WAAU;8EACX,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,kBAClC,8OAAC;4EAAW,WAAU;;8FACpB,8OAAC;oFAAK,WAAU;;;;;;gFACf;;2EAFM;;;;;;;;;;;;;;;;;mDArBP,IAAI,EAAE;;;;;0DAgClB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsD;;;;;;kEAGpE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;0EAE/C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;0EAE/C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjE;uCAEe", "debugId": null}}, {"offset": {"line": 2779, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/portfolio/src/components/YouTube.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Play, ExternalLink, Calendar, Eye, ThumbsUp, Youtube } from 'lucide-react';\nimport Image from 'next/image';\n\ninterface YouTubeVideo {\n  id: string;\n  title: string;\n  description: string;\n  thumbnail: string;\n  publishedAt: string;\n  viewCount: string;\n  likeCount: string;\n  duration: string;\n  url: string;\n}\n\nconst YouTube = () => {\n  const [videos, setVideos] = useState<YouTubeVideo[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Mock data for demonstration - replace with actual API call\n  const mockVideos: YouTubeVideo[] = [\n    {\n      id: '1',\n      title: 'Building a Modern React Portfolio with Next.js and Framer Motion',\n      description: 'Learn how to create a stunning portfolio website using Next.js, TypeScript, and Framer Motion animations.',\n      thumbnail: '/youtube-thumb-1.jpg',\n      publishedAt: '2024-01-15',\n      viewCount: '12,543',\n      likeCount: '892',\n      duration: '15:32',\n      url: 'https://youtube.com/watch?v=example1'\n    },\n    {\n      id: '2',\n      title: 'Advanced CSS Animations and Micro-interactions',\n      description: 'Dive deep into CSS animations and learn how to create engaging micro-interactions for better UX.',\n      thumbnail: '/youtube-thumb-2.jpg',\n      publishedAt: '2024-01-08',\n      viewCount: '8,721',\n      likeCount: '654',\n      duration: '22:18',\n      url: 'https://youtube.com/watch?v=example2'\n    },\n    {\n      id: '3',\n      title: 'TypeScript Best Practices for React Developers',\n      description: 'Essential TypeScript patterns and best practices every React developer should know.',\n      thumbnail: '/youtube-thumb-3.jpg',\n      publishedAt: '2024-01-01',\n      viewCount: '15,892',\n      likeCount: '1,234',\n      duration: '18:45',\n      url: 'https://youtube.com/watch?v=example3'\n    },\n    {\n      id: '4',\n      title: 'Creating Responsive Designs with Tailwind CSS',\n      description: 'Master responsive design principles using Tailwind CSS utility classes and custom configurations.',\n      thumbnail: '/youtube-thumb-4.jpg',\n      publishedAt: '2023-12-25',\n      viewCount: '9,876',\n      likeCount: '743',\n      duration: '12:56',\n      url: 'https://youtube.com/watch?v=example4'\n    },\n    {\n      id: '5',\n      title: 'UI/UX Design Process: From Wireframes to Prototype',\n      description: 'Complete walkthrough of my design process from initial wireframes to interactive prototypes.',\n      thumbnail: '/youtube-thumb-5.jpg',\n      publishedAt: '2023-12-18',\n      viewCount: '6,543',\n      likeCount: '521',\n      duration: '25:12',\n      url: 'https://youtube.com/watch?v=example5'\n    },\n    {\n      id: '6',\n      title: 'Performance Optimization Techniques for React Apps',\n      description: 'Learn advanced techniques to optimize your React applications for better performance and user experience.',\n      thumbnail: '/youtube-thumb-6.jpg',\n      publishedAt: '2023-12-11',\n      viewCount: '11,234',\n      likeCount: '876',\n      duration: '19:33',\n      url: 'https://youtube.com/watch?v=example6'\n    }\n  ];\n\n  useEffect(() => {\n    const fetchVideos = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch('/api/youtube-videos');\n\n        if (!response.ok) {\n          throw new Error('Failed to fetch videos');\n        }\n\n        const data = await response.json();\n        setVideos(data);\n        setError(null);\n      } catch (err) {\n        setError('Failed to load videos');\n        console.error('Error fetching YouTube videos:', err);\n        // Fallback to mock data\n        setVideos(mockVideos);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchVideos();\n  }, []);\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6\n      }\n    }\n  };\n\n  if (loading) {\n    return (\n      <section id=\"youtube\" className=\"py-20\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin w-12 h-12 border-4 border-accent border-t-transparent rounded-full mx-auto\"></div>\n            <p className=\"mt-4 text-foreground/70\">Loading latest videos...</p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (error) {\n    return (\n      <section id=\"youtube\" className=\"py-20\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"text-center\">\n            <p className=\"text-red-500\">{error}</p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section id=\"youtube\" className=\"py-20 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-64 h-64 bg-accent/10 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-4\">\n            <span className=\"gradient-accent bg-clip-text text-transparent\">\n              Latest YouTube Videos\n            </span>\n          </h2>\n          <p className=\"text-lg text-foreground/70 max-w-2xl mx-auto mb-8\">\n            Sharing knowledge through tutorials, tips, and insights about web development and design\n          </p>\n\n          {/* YouTube Channel Link */}\n          <motion.a\n            href=\"https://youtube.com/@yourchannel\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"inline-flex items-center gap-2 px-6 py-3 bg-red-600 hover:bg-red-700\n                     text-white rounded-full font-semibold transition-colors duration-300\"\n          >\n            <Youtube className=\"w-5 h-5\" />\n            Subscribe to Channel\n          </motion.a>\n        </motion.div>\n\n        {/* Videos Grid */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n        >\n          {videos.slice(0, 6).map((video) => (\n            <motion.div\n              key={video.id}\n              variants={itemVariants}\n              className=\"group glass-strong rounded-2xl overflow-hidden hover:scale-105\n                       transition-all duration-500 cursor-pointer\"\n              onClick={() => window.open(video.url, '_blank')}\n            >\n              {/* Video Thumbnail */}\n              <div className=\"relative aspect-video overflow-hidden\">\n                <Image\n                  src={video.thumbnail}\n                  alt={video.title}\n                  fill\n                  className=\"object-cover group-hover:scale-110 transition-transform duration-500\"\n                />\n\n                {/* Play Button Overlay */}\n                <div className=\"absolute inset-0 bg-black/40 flex items-center justify-center\n                              opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                  <motion.div\n                    whileHover={{ scale: 1.1 }}\n                    className=\"w-16 h-16 bg-red-600 rounded-full flex items-center justify-center\"\n                  >\n                    <Play className=\"w-6 h-6 text-white ml-1\" fill=\"currentColor\" />\n                  </motion.div>\n                </div>\n\n                {/* Duration Badge */}\n                <div className=\"absolute bottom-2 right-2 px-2 py-1 bg-black/80 text-white\n                              text-xs font-semibold rounded\">\n                  {video.duration}\n                </div>\n              </div>\n\n              {/* Video Content */}\n              <div className=\"p-6\">\n                <h3 className=\"text-lg font-semibold font-display mb-2 line-clamp-2\n                             group-hover:text-accent transition-colors duration-300\">\n                  {video.title}\n                </h3>\n\n                <p className=\"text-foreground/70 text-sm mb-4 line-clamp-3\">\n                  {video.description}\n                </p>\n\n                {/* Video Stats */}\n                <div className=\"flex items-center justify-between text-xs text-foreground/60 mb-4\">\n                  <div className=\"flex items-center gap-4\">\n                    <span className=\"flex items-center gap-1\">\n                      <Eye className=\"w-3 h-3\" />\n                      {video.viewCount}\n                    </span>\n                    <span className=\"flex items-center gap-1\">\n                      <ThumbsUp className=\"w-3 h-3\" />\n                      {video.likeCount}\n                    </span>\n                  </div>\n                  <span className=\"flex items-center gap-1\">\n                    <Calendar className=\"w-3 h-3\" />\n                    {formatDate(video.publishedAt)}\n                  </span>\n                </div>\n\n                {/* Watch Button */}\n                <motion.button\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"w-full flex items-center justify-center gap-2 py-2 px-4\n                           bg-gradient-accent text-white rounded-lg font-medium\n                           hover:shadow-lg transition-all duration-300\"\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    window.open(video.url, '_blank');\n                  }}\n                >\n                  <Play className=\"w-4 h-4\" fill=\"currentColor\" />\n                  Watch Video\n                  <ExternalLink className=\"w-4 h-4\" />\n                </motion.button>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* View All Videos Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"text-center mt-12\"\n        >\n          <motion.a\n            href=\"https://youtube.com/@yourchannel/videos\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"inline-flex items-center gap-2 px-8 py-4 glass hover:glass-strong\n                     rounded-full font-semibold border border-accent/30 hover:border-accent/50\n                     transition-all duration-300\"\n          >\n            <Youtube className=\"w-5 h-5\" />\n            View All Videos\n            <ExternalLink className=\"w-4 h-4\" />\n          </motion.a>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default YouTube;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAmBA,MAAM,UAAU;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,6DAA6D;IAC7D,MAAM,aAA6B;QACjC;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;YACX,WAAW;YACX,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;YACX,WAAW;YACX,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;YACX,WAAW;YACX,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;YACX,WAAW;YACX,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;YACX,WAAW;YACX,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;YACX,WAAW;YACX,UAAU;YACV,KAAK;QACP;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,MAAM;gBAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,UAAU;gBACV,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,SAAS;gBACT,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,wBAAwB;gBACxB,UAAU;YACZ,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAQ,IAAG;YAAU,WAAU;sBAC9B,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAA0B;;;;;;;;;;;;;;;;;;;;;;IAKjD;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAQ,IAAG;YAAU,WAAU;sBAC9B,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;;0BAE9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;0CAIlE,8OAAC;gCAAE,WAAU;0CAAoD;;;;;;0CAKjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAGV,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAMnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAET,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAU;gCAEV,SAAS,IAAM,OAAO,IAAI,CAAC,MAAM,GAAG,EAAE;;kDAGtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,MAAM,SAAS;gDACpB,KAAK,MAAM,KAAK;gDAChB,IAAI;gDACJ,WAAU;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;0DAEb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,YAAY;wDAAE,OAAO;oDAAI;oDACzB,WAAU;8DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;wDAA0B,MAAK;;;;;;;;;;;;;;;;0DAKnD,8OAAC;gDAAI,WAAU;0DAEZ,MAAM,QAAQ;;;;;;;;;;;;kDAKnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAEX,MAAM,KAAK;;;;;;0DAGd,8OAAC;gDAAE,WAAU;0DACV,MAAM,WAAW;;;;;;0DAIpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;kFACd,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEACd,MAAM,SAAS;;;;;;;0EAElB,8OAAC;gEAAK,WAAU;;kFACd,8OAAC,8MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEACnB,MAAM,SAAS;;;;;;;;;;;;;kEAGpB,8OAAC;wDAAK,WAAU;;0EACd,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,WAAW,MAAM,WAAW;;;;;;;;;;;;;0DAKjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;gDAGV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,OAAO,IAAI,CAAC,MAAM,GAAG,EAAE;gDACzB;;kEAEA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;wDAAU,MAAK;;;;;;oDAAiB;kEAEhD,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;+BA5EvB,MAAM,EAAE;;;;;;;;;;kCAoFnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,MAAK;4BACL,QAAO;4BACP,KAAI;4BACJ,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;;8CAIV,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAY;8CAE/B,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;uCAEe", "debugId": null}}, {"offset": {"line": 3379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/portfolio/src/components/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  Mail, \n  Phone, \n  MapPin, \n  Send, \n  Github, \n  Linkedin, \n  Twitter,\n  CheckCircle,\n  AlertCircle\n} from 'lucide-react';\n\nconst Contact = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n\n  const contactInfo = [\n    {\n      icon: Mail,\n      label: 'Email',\n      value: '<EMAIL>',\n      href: 'mailto:<EMAIL>'\n    },\n    {\n      icon: Phone,\n      label: 'Phone',\n      value: '+****************',\n      href: 'tel:+15551234567'\n    },\n    {\n      icon: MapPin,\n      label: 'Location',\n      value: 'San Francisco, CA',\n      href: 'https://maps.google.com/?q=San+Francisco,+CA'\n    }\n  ];\n\n  const socialLinks = [\n    {\n      icon: Github,\n      label: 'GitHub',\n      href: 'https://github.com/yourusername',\n      color: 'hover:text-gray-700'\n    },\n    {\n      icon: Linkedin,\n      label: 'LinkedIn',\n      href: 'https://linkedin.com/in/yourusername',\n      color: 'hover:text-blue-600'\n    },\n    {\n      icon: Twitter,\n      label: 'Twitter',\n      href: 'https://twitter.com/yourusername',\n      color: 'hover:text-blue-400'\n    }\n  ];\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      // Simulate form submission\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      // In a real implementation, you would send the form data to your backend\n      // const response = await fetch('/api/contact', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify(formData)\n      // });\n\n      setSubmitStatus('success');\n      setFormData({ name: '', email: '', subject: '', message: '' });\n    } catch (error) {\n      setSubmitStatus('error');\n      console.error('Error submitting form:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const isFormValid = formData.name && formData.email && formData.message;\n\n  return (\n    <section id=\"contact\" className=\"py-20 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/4 right-1/4 w-96 h-96 bg-accent/5 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-1/4 left-1/4 w-96 h-96 bg-accent-secondary/5 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold font-display mb-4\">\n            <span className=\"gradient-accent bg-clip-text text-transparent\">\n              Let's Work Together\n            </span>\n          </h2>\n          <p className=\"text-lg text-foreground/70 max-w-2xl mx-auto\">\n            Have a project in mind? I'd love to hear about it. Let's discuss how we can bring your ideas to life.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12\">\n          {/* Contact Information */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8 }}\n            className=\"space-y-8\"\n          >\n            <div>\n              <h3 className=\"text-2xl font-semibold font-display mb-6\">Get in Touch</h3>\n              <p className=\"text-foreground/70 mb-8\">\n                I'm always open to discussing new opportunities, creative projects, \n                or just having a chat about technology and design.\n              </p>\n            </div>\n\n            {/* Contact Info */}\n            <div className=\"space-y-6\">\n              {contactInfo.map((info, index) => (\n                <motion.a\n                  key={info.label}\n                  href={info.href}\n                  target={info.href.startsWith('http') ? '_blank' : undefined}\n                  rel={info.href.startsWith('http') ? 'noopener noreferrer' : undefined}\n                  initial={{ opacity: 0, x: -20 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  viewport={{ once: true }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  whileHover={{ x: 5 }}\n                  className=\"flex items-center gap-4 p-4 glass hover:glass-strong rounded-xl \n                           transition-all duration-300 group\"\n                >\n                  <div className=\"p-3 bg-gradient-accent rounded-lg group-hover:scale-110 transition-transform\">\n                    <info.icon className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div>\n                    <p className=\"font-medium\">{info.label}</p>\n                    <p className=\"text-foreground/70 group-hover:text-accent transition-colors\">\n                      {info.value}\n                    </p>\n                  </div>\n                </motion.a>\n              ))}\n            </div>\n\n            {/* Social Links */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">Follow Me</h4>\n              <div className=\"flex gap-4\">\n                {socialLinks.map((social, index) => (\n                  <motion.a\n                    key={social.label}\n                    href={social.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    initial={{ opacity: 0, scale: 0 }}\n                    whileInView={{ opacity: 1, scale: 1 }}\n                    viewport={{ once: true }}\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\n                    whileHover={{ scale: 1.1, y: -2 }}\n                    className={`p-3 glass hover:glass-strong rounded-full transition-all duration-300 ${social.color}`}\n                    aria-label={social.label}\n                  >\n                    <social.icon className=\"w-5 h-5\" />\n                  </motion.a>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8 }}\n            className=\"glass-strong rounded-2xl p-8\"\n          >\n            <h3 className=\"text-2xl font-semibold font-display mb-6\">Send a Message</h3>\n            \n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* Name and Email */}\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium mb-2\">\n                    Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    required\n                    className=\"w-full px-4 py-3 glass border border-glass-border rounded-lg \n                             focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent \n                             transition-all duration-300\"\n                    placeholder=\"Your name\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium mb-2\">\n                    Email *\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    required\n                    className=\"w-full px-4 py-3 glass border border-glass-border rounded-lg \n                             focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent \n                             transition-all duration-300\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                </div>\n              </div>\n\n              {/* Subject */}\n              <div>\n                <label htmlFor=\"subject\" className=\"block text-sm font-medium mb-2\">\n                  Subject\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"subject\"\n                  name=\"subject\"\n                  value={formData.subject}\n                  onChange={handleInputChange}\n                  className=\"w-full px-4 py-3 glass border border-glass-border rounded-lg \n                           focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent \n                           transition-all duration-300\"\n                  placeholder=\"What's this about?\"\n                />\n              </div>\n\n              {/* Message */}\n              <div>\n                <label htmlFor=\"message\" className=\"block text-sm font-medium mb-2\">\n                  Message *\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleInputChange}\n                  required\n                  rows={5}\n                  className=\"w-full px-4 py-3 glass border border-glass-border rounded-lg \n                           focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent \n                           transition-all duration-300 resize-none\"\n                  placeholder=\"Tell me about your project...\"\n                />\n              </div>\n\n              {/* Submit Button */}\n              <motion.button\n                type=\"submit\"\n                disabled={!isFormValid || isSubmitting}\n                whileHover={{ scale: isFormValid ? 1.02 : 1 }}\n                whileTap={{ scale: isFormValid ? 0.98 : 1 }}\n                className={`w-full flex items-center justify-center gap-2 py-4 px-6 rounded-lg \n                          font-semibold transition-all duration-300 ${\n                  isFormValid && !isSubmitting\n                    ? 'bg-gradient-accent text-white hover:shadow-lg hover:shadow-accent/25'\n                    : 'bg-muted text-foreground/50 cursor-not-allowed'\n                }`}\n              >\n                {isSubmitting ? (\n                  <>\n                    <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n                    Sending...\n                  </>\n                ) : (\n                  <>\n                    <Send className=\"w-5 h-5\" />\n                    Send Message\n                  </>\n                )}\n              </motion.button>\n\n              {/* Status Messages */}\n              {submitStatus === 'success' && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"flex items-center gap-2 p-4 bg-green-500/10 border border-green-500/20 \n                           rounded-lg text-green-600\"\n                >\n                  <CheckCircle className=\"w-5 h-5\" />\n                  Message sent successfully! I'll get back to you soon.\n                </motion.div>\n              )}\n\n              {submitStatus === 'error' && (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"flex items-center gap-2 p-4 bg-red-500/10 border border-red-500/20 \n                           rounded-lg text-red-600\"\n                >\n                  <AlertCircle className=\"w-5 h-5\" />\n                  Failed to send message. Please try again or contact me directly.\n                </motion.div>\n              )}\n            </form>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAgBA,MAAM,UAAU;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAE/E,MAAM,cAAc;QAClB;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,0MAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,yEAAyE;YACzE,iDAAiD;YACjD,oBAAoB;YACpB,qDAAqD;YACrD,mCAAmC;YACnC,MAAM;YAEN,gBAAgB;YAChB,YAAY;gBAAE,MAAM;gBAAI,OAAO;gBAAI,SAAS;gBAAI,SAAS;YAAG;QAC9D,EAAE,OAAO,OAAO;YACd,gBAAgB;YAChB,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,SAAS,IAAI,IAAI,SAAS,KAAK,IAAI,SAAS,OAAO;IAEvE,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;;0BAE9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;0CAIlE,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;;;;;;;kCAK9D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;kDAOzC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDAEP,MAAM,KAAK,IAAI;gDACf,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,WAAW;gDAClD,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,UAAU,wBAAwB;gDAC5D,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDAAE,UAAU;oDAAK,OAAO,QAAQ;gDAAI;gDAChD,YAAY;oDAAE,GAAG;gDAAE;gDACnB,WAAU;;kEAGV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,KAAK,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAEvB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAe,KAAK,KAAK;;;;;;0EACtC,8OAAC;gEAAE,WAAU;0EACV,KAAK,KAAK;;;;;;;;;;;;;+CAlBV,KAAK,KAAK;;;;;;;;;;kDA0BrB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wDAEP,MAAM,OAAO,IAAI;wDACjB,QAAO;wDACP,KAAI;wDACJ,SAAS;4DAAE,SAAS;4DAAG,OAAO;wDAAE;wDAChC,aAAa;4DAAE,SAAS;4DAAG,OAAO;wDAAE;wDACpC,UAAU;4DAAE,MAAM;wDAAK;wDACvB,YAAY;4DAAE,UAAU;4DAAK,OAAO,QAAQ;wDAAI;wDAChD,YAAY;4DAAE,OAAO;4DAAK,GAAG,CAAC;wDAAE;wDAChC,WAAW,CAAC,sEAAsE,EAAE,OAAO,KAAK,EAAE;wDAClG,cAAY,OAAO,KAAK;kEAExB,cAAA,8OAAC,OAAO,IAAI;4DAAC,WAAU;;;;;;uDAZlB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;0CAoB3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAEzD,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DAEtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAO,WAAU;0EAAiC;;;;;;0EAGjE,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEAGV,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAAiC;;;;;;0EAGlE,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEAGV,aAAY;;;;;;;;;;;;;;;;;;0DAMlB,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAiC;;;;;;kEAGpE,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAU;wDAGV,aAAY;;;;;;;;;;;;0DAKhB,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAiC;;;;;;kEAGpE,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,QAAQ;wDACR,MAAM;wDACN,WAAU;wDAGV,aAAY;;;;;;;;;;;;0DAKhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,MAAK;gDACL,UAAU,CAAC,eAAe;gDAC1B,YAAY;oDAAE,OAAO,cAAc,OAAO;gDAAE;gDAC5C,UAAU;oDAAE,OAAO,cAAc,OAAO;gDAAE;gDAC1C,WAAW,CAAC;oEACwC,EAClD,eAAe,CAAC,eACZ,yEACA,kDACJ;0DAED,6BACC;;sEACE,8OAAC;4DAAI,WAAU;;;;;;wDAAiF;;iFAIlG;;sEACE,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;4CAOjC,iBAAiB,2BAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,WAAU;;kEAGV,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAY;;;;;;;4CAKtC,iBAAiB,yBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,WAAU;;kEAGV,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;uCAEe", "debugId": null}}, {"offset": {"line": 4032, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/portfolio/portfolio/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  Github, \n  Linkedin, \n  Twitter, \n  Mail, \n  Heart, \n  ArrowUp,\n  Code2,\n  Palette\n} from 'lucide-react';\nimport { useTheme } from '@/contexts/ThemeContext';\n\nconst Footer = () => {\n  const { theme, toggleTheme } = useTheme();\n\n  const socialLinks = [\n    {\n      icon: Github,\n      label: 'GitHub',\n      href: 'https://github.com/yourusername',\n      color: 'hover:text-gray-700 dark:hover:text-gray-300'\n    },\n    {\n      icon: Linkedin,\n      label: 'LinkedIn',\n      href: 'https://linkedin.com/in/yourusername',\n      color: 'hover:text-blue-600'\n    },\n    {\n      icon: Twitter,\n      label: 'Twitter',\n      href: 'https://twitter.com/yourusername',\n      color: 'hover:text-blue-400'\n    },\n    {\n      icon: Mail,\n      label: 'Email',\n      href: 'mailto:<EMAIL>',\n      color: 'hover:text-accent'\n    }\n  ];\n\n  const quickLinks = [\n    { name: 'Home', href: '#home' },\n    { name: 'About', href: '#about' },\n    { name: 'Skills', href: '#skills' },\n    { name: 'Projects', href: '#projects' },\n    { name: 'Experience', href: '#experience' },\n    { name: 'YouTube', href: '#youtube' },\n    { name: 'Contact', href: '#contact' }\n  ];\n\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <footer className=\"relative bg-gradient-primary border-t border-glass-border\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-0 left-1/4 w-64 h-64 bg-accent/5 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-0 right-1/4 w-64 h-64 bg-accent-secondary/5 rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6 py-12\">\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8\">\n          {/* Brand Section */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.6 }}\n            className=\"lg:col-span-2\"\n          >\n            <div className=\"mb-4\">\n              <h3 className=\"text-2xl font-bold font-display\">\n                <span className=\"gradient-neon bg-clip-text text-transparent animate-gradient\">\n                  Portfolio\n                </span>\n              </h3>\n            </div>\n            <p className=\"text-foreground/70 mb-6 max-w-md\">\n              Creative developer and UI/UX designer passionate about crafting beautiful, \n              functional digital experiences with modern technologies.\n            </p>\n            \n            {/* Social Links */}\n            <div className=\"flex gap-4\">\n              {socialLinks.map((social, index) => (\n                <motion.a\n                  key={social.label}\n                  href={social.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  initial={{ opacity: 0, scale: 0 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  viewport={{ once: true }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  whileHover={{ scale: 1.1, y: -2 }}\n                  className={`p-3 glass hover:glass-strong rounded-full transition-all duration-300 ${social.color}`}\n                  aria-label={social.label}\n                >\n                  <social.icon className=\"w-5 h-5\" />\n                </motion.a>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Quick Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n          >\n            <h4 className=\"text-lg font-semibold font-display mb-4\">Quick Links</h4>\n            <ul className=\"space-y-2\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <button\n                    onClick={() => scrollToSection(link.href)}\n                    className=\"text-foreground/70 hover:text-accent transition-colors duration-300 \n                             hover:translate-x-1 transform transition-transform\"\n                  >\n                    {link.name}\n                  </button>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Services */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n          >\n            <h4 className=\"text-lg font-semibold font-display mb-4\">Services</h4>\n            <ul className=\"space-y-2\">\n              <li className=\"flex items-center gap-2 text-foreground/70\">\n                <Code2 className=\"w-4 h-4 text-accent\" />\n                Web Development\n              </li>\n              <li className=\"flex items-center gap-2 text-foreground/70\">\n                <Palette className=\"w-4 h-4 text-accent\" />\n                UI/UX Design\n              </li>\n              <li className=\"flex items-center gap-2 text-foreground/70\">\n                <Code2 className=\"w-4 h-4 text-accent\" />\n                Frontend Development\n              </li>\n              <li className=\"flex items-center gap-2 text-foreground/70\">\n                <Palette className=\"w-4 h-4 text-accent\" />\n                Responsive Design\n              </li>\n            </ul>\n          </motion.div>\n        </div>\n\n        {/* Bottom Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          className=\"border-t border-glass-border pt-8\"\n        >\n          <div className=\"flex flex-col md:flex-row items-center justify-between gap-4\">\n            {/* Copyright */}\n            <div className=\"flex items-center gap-2 text-foreground/70\">\n              <span>© 2024 Portfolio. Made with</span>\n              <motion.div\n                animate={{ scale: [1, 1.2, 1] }}\n                transition={{ duration: 1, repeat: Infinity }}\n              >\n                <Heart className=\"w-4 h-4 text-red-500 fill-current\" />\n              </motion.div>\n              <span>using Next.js & Tailwind CSS</span>\n            </div>\n\n            {/* Theme Toggle & Scroll to Top */}\n            <div className=\"flex items-center gap-4\">\n              {/* Theme Toggle */}\n              <motion.button\n                onClick={toggleTheme}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"p-2 glass hover:glass-strong rounded-full transition-all duration-300\"\n                aria-label=\"Toggle theme\"\n              >\n                {theme === 'dark' ? '🌞' : '🌙'}\n              </motion.button>\n\n              {/* Scroll to Top */}\n              <motion.button\n                onClick={scrollToTop}\n                whileHover={{ scale: 1.05, y: -2 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"p-3 bg-gradient-accent text-white rounded-full hover:shadow-lg \n                         hover:shadow-accent/25 transition-all duration-300\"\n                aria-label=\"Scroll to top\"\n              >\n                <ArrowUp className=\"w-5 h-5\" />\n              </motion.button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Decorative Elements */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gradient-accent\"></div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAdA;;;;;AAgBA,MAAM,SAAS;IACb,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEtC,MAAM,cAAc;QAClB;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAK,WAAU;0DAA+D;;;;;;;;;;;;;;;;kDAKnF,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAMhD,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDAEP,MAAM,OAAO,IAAI;gDACjB,QAAO;gDACP,KAAI;gDACJ,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,aAAa;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDACpC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDAAE,UAAU;oDAAK,OAAO,QAAQ;gDAAI;gDAChD,YAAY;oDAAE,OAAO;oDAAK,GAAG,CAAC;gDAAE;gDAChC,WAAW,CAAC,sEAAsE,EAAE,OAAO,KAAK,EAAE;gDAClG,cAAY,OAAO,KAAK;0DAExB,cAAA,8OAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;+CAZlB,OAAO,KAAK;;;;;;;;;;;;;;;;0CAmBzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAG,WAAU;kDACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;0DACC,cAAA,8OAAC;oDACC,SAAS,IAAM,gBAAgB,KAAK,IAAI;oDACxC,WAAU;8DAGT,KAAK,IAAI;;;;;;+CANL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAcxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,0MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG3C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG7C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,0MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG3C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;kCAQnD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;sDAE5C,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;sDAAK;;;;;;;;;;;;8CAIR,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS;4CACT,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;4CACV,cAAW;sDAEV,UAAU,SAAS,OAAO;;;;;;sDAI7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS;4CACT,YAAY;gDAAE,OAAO;gDAAM,GAAG,CAAC;4CAAE;4CACjC,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;4CAEV,cAAW;sDAEX,cAAA,8OAAC,4MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7B,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}]}