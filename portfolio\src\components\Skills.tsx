'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Code2, 
  Palette, 
  Database, 
  Globe, 
  Smartphone, 
  Zap,
  Layers,
  GitBranch,
  Monitor,
  Figma,
  Cpu,
  Cloud
} from 'lucide-react';

const Skills = () => {
  const skillCategories = [
    {
      title: 'Frontend Development',
      icon: Code2,
      skills: [
        { name: 'React', level: 95, color: 'from-blue-400 to-blue-600' },
        { name: 'Next.js', level: 90, color: 'from-gray-700 to-black' },
        { name: 'TypeScript', level: 88, color: 'from-blue-500 to-blue-700' },
        { name: 'Tailwind CSS', level: 92, color: 'from-cyan-400 to-cyan-600' },
        { name: 'Framer Motion', level: 85, color: 'from-purple-400 to-purple-600' },
      ]
    },
    {
      title: 'UI/UX Design',
      icon: Palette,
      skills: [
        { name: 'Figma', level: 90, color: 'from-purple-400 to-pink-400' },
        { name: 'Adobe XD', level: 85, color: 'from-pink-400 to-red-400' },
        { name: 'Prototyping', level: 88, color: 'from-green-400 to-green-600' },
        { name: 'User Research', level: 82, color: 'from-yellow-400 to-orange-400' },
        { name: 'Design Systems', level: 87, color: 'from-indigo-400 to-indigo-600' },
      ]
    },
    {
      title: 'Backend & Tools',
      icon: Database,
      skills: [
        { name: 'Node.js', level: 80, color: 'from-green-500 to-green-700' },
        { name: 'Python', level: 75, color: 'from-yellow-400 to-yellow-600' },
        { name: 'PostgreSQL', level: 78, color: 'from-blue-600 to-blue-800' },
        { name: 'Git & GitHub', level: 90, color: 'from-gray-600 to-gray-800' },
        { name: 'Docker', level: 70, color: 'from-blue-400 to-blue-600' },
      ]
    }
  ];

  const techStack = [
    { name: 'React', icon: Globe, color: 'text-blue-400' },
    { name: 'Next.js', icon: Zap, color: 'text-gray-700' },
    { name: 'TypeScript', icon: Code2, color: 'text-blue-500' },
    { name: 'Tailwind', icon: Layers, color: 'text-cyan-400' },
    { name: 'Figma', icon: Figma, color: 'text-purple-400' },
    { name: 'Git', icon: GitBranch, color: 'text-orange-400' },
    { name: 'Responsive', icon: Smartphone, color: 'text-green-400' },
    { name: 'Performance', icon: Cpu, color: 'text-red-400' },
    { name: 'Cloud', icon: Cloud, color: 'text-blue-300' },
    { name: 'Modern UI', icon: Monitor, color: 'text-pink-400' },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section id="skills" className="py-20 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-accent/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-accent-secondary/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold font-display mb-4">
            <span className="gradient-accent bg-clip-text text-transparent">
              Skills & Expertise
            </span>
          </h2>
          <p className="text-lg text-foreground/70 max-w-2xl mx-auto">
            A comprehensive toolkit for creating exceptional digital experiences
          </p>
        </motion.div>

        {/* Skills Categories */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
        >
          {skillCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              variants={itemVariants}
              className="glass-strong rounded-2xl p-6 hover:scale-105 transition-transform duration-300"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-gradient-accent rounded-lg">
                  <category.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold font-display">{category.title}</h3>
              </div>

              <div className="space-y-4">
                {category.skills.map((skill, skillIndex) => (
                  <div key={skill.name} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{skill.name}</span>
                      <span className="text-sm text-accent">{skill.level}%</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
                      <motion.div
                        initial={{ width: 0 }}
                        whileInView={{ width: `${skill.level}%` }}
                        viewport={{ once: true }}
                        transition={{ 
                          duration: 1, 
                          delay: categoryIndex * 0.2 + skillIndex * 0.1 
                        }}
                        className={`h-full bg-gradient-to-r ${skill.color} rounded-full`}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Tech Stack Icons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="text-center"
        >
          <h3 className="text-2xl font-semibold font-display mb-8">
            Technologies I Work With
          </h3>
          
          <div className="flex flex-wrap justify-center gap-6">
            {techStack.map((tech, index) => (
              <motion.div
                key={tech.name}
                initial={{ opacity: 0, scale: 0 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ 
                  duration: 0.5, 
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{ 
                  scale: 1.1, 
                  y: -5,
                  transition: { duration: 0.2 }
                }}
                className="group flex flex-col items-center gap-2 p-4 glass hover:glass-strong 
                         rounded-xl transition-all duration-300 cursor-pointer"
              >
                <tech.icon className={`w-8 h-8 ${tech.color} group-hover:scale-110 transition-transform`} />
                <span className="text-sm font-medium group-hover:text-accent transition-colors">
                  {tech.name}
                </span>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Skills;
