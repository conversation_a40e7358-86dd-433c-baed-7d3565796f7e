# 🚀 Futuristic Portfolio Website

A modern, elegant, and fully responsive personal portfolio website built with Next.js, Tailwind CSS, and TypeScript. Features stunning animations, glassmorphism design, and dynamic content integration.

![Portfolio Preview](https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=400&fit=crop)

## ✨ Features

- **Modern Design**: Sleek, Apple-like visual style with glassmorphism elements
- **Fully Responsive**: Optimized for mobile, tablet, and desktop devices
- **Dark/Light Mode**: Smooth theme switching with system preference detection
- **Smooth Animations**: Framer Motion powered animations and micro-interactions
- **Dynamic Content**: YouTube API integration for latest videos
- **Interactive Sections**:
  - Hero section with animated greeting
  - Skills showcase with progress bars
  - Project gallery with filtering
  - Experience timeline
  - YouTube videos integration
  - Contact form with validation
- **SEO Optimized**: Meta tags, structured data, and accessibility features
- **Performance Focused**: Optimized images, lazy loading, and fast loading times

## 🛠️ Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Fonts**: Inter & Sora (Google Fonts)
- **API**: YouTube Data API v3

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/portfolio.git
   cd portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` and add your configuration:
   ```env
   YOUTUBE_API_KEY=your_youtube_api_key_here
   YOUTUBE_CHANNEL_ID=your_channel_id_here
   ```

4. **Add your images**

   Place the following images in the `public` folder:
   - `profile-placeholder.jpg` - Your profile photo
   - `project-1.jpg` to `project-6.jpg` - Project screenshots
   - `youtube-thumb-1.jpg` to `youtube-thumb-6.jpg` - Video thumbnails
   - `resume.pdf` - Your resume

5. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

6. **Open your browser**

   Navigate to [http://localhost:3000](http://localhost:3000)

## 📝 Customization

### Personal Information

Edit the following files to customize with your information:

- `src/components/Hero.tsx` - Name, title, and description
- `src/components/Skills.tsx` - Your skills and technologies
- `src/components/Projects.tsx` - Your projects and portfolio items
- `src/components/Experience.tsx` - Work experience and education
- `src/components/Contact.tsx` - Contact information and social links
- `src/app/layout.tsx` - Meta tags and SEO information

### Styling

The design system is configured in `src/app/globals.css`:

- **Colors**: Modify CSS custom properties for theme colors
- **Fonts**: Update font imports and variables
- **Animations**: Customize keyframes and transitions
- **Glassmorphism**: Adjust backdrop-blur and transparency values

### YouTube Integration

To enable YouTube video fetching:

1. Get a YouTube Data API key from [Google Cloud Console](https://console.developers.google.com/)
2. Find your YouTube Channel ID
3. Add both to your `.env.local` file

If not configured, the app will display mock video data.

## 📱 Responsive Design

The portfolio is fully responsive with breakpoints:

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🎨 Design Features

- **Glassmorphism**: Translucent cards with backdrop blur
- **Neon Accents**: Glowing effects and gradient borders
- **Smooth Scrolling**: Animated section transitions
- **Hover Effects**: Interactive micro-animations
- **Loading States**: Skeleton screens and spinners
- **Form Validation**: Real-time input validation

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Add environment variables in Vercel dashboard
4. Deploy automatically on every push

### Other Platforms

The app can be deployed to any platform that supports Next.js:

- **Netlify**: Use `npm run build` and deploy the `out` folder
- **AWS Amplify**: Connect your GitHub repository
- **Railway**: Deploy directly from GitHub
- **DigitalOcean App Platform**: Use the web interface

## 📊 Performance

- **Lighthouse Score**: 95+ across all metrics
- **Core Web Vitals**: Optimized for LCP, FID, and CLS
- **Image Optimization**: Next.js automatic image optimization
- **Code Splitting**: Automatic route-based code splitting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - React framework
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [Framer Motion](https://www.framer.com/motion/) - Animation library
- [Lucide](https://lucide.dev/) - Icon library
- [Unsplash](https://unsplash.com/) - Stock photos

## 📞 Support

If you have any questions or need help with setup, feel free to:

- Open an issue on GitHub
- Contact me at [<EMAIL>](mailto:<EMAIL>)
- Connect with me on [LinkedIn](https://linkedin.com/in/yourusername)

---

**Made with ❤️ using Next.js and Tailwind CSS**
