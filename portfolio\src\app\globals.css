@import "tailwindcss";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Sora:wght@100;200;300;400;500;600;700;800&display=swap');

:root {
  /* Light theme colors */
  --background: #ffffff;
  --foreground: #1a1a2e;
  --primary: #0f3460;
  --secondary: #16213e;
  --accent: #00d4aa;
  --accent-secondary: #00b4d8;
  --muted: #f8fafc;
  --border: #e2e8f0;
  --card: #ffffff;
  --card-foreground: #1a1a2e;
  --neon: #00ffff;
  --neon-pink: #ff006e;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] {
  /* Dark theme colors */
  --background: #0a0a0f;
  --foreground: #ffffff;
  --primary: #1a1a2e;
  --secondary: #16213e;
  --accent: #00d4aa;
  --accent-secondary: #00b4d8;
  --muted: #1e293b;
  --border: #334155;
  --card: #1e293b;
  --card-foreground: #ffffff;
  --neon: #00ffff;
  --neon-pink: #ff006e;
  --glass-bg: rgba(0, 0, 0, 0.3);
  --glass-border: rgba(255, 255, 255, 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Font families */
.font-display {
  font-family: 'Sora', system-ui, -apple-system, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-secondary);
}

/* Glassmorphism utility classes */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.glass-strong {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, var(--accent) 0%, var(--accent-secondary) 100%);
}

.gradient-neon {
  background: linear-gradient(135deg, var(--neon) 0%, var(--neon-pink) 100%);
}

/* Animation utilities */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-neon {
  0%, 100% { box-shadow: 0 0 5px var(--neon), 0 0 10px var(--neon), 0 0 15px var(--neon); }
  50% { box-shadow: 0 0 10px var(--neon), 0 0 20px var(--neon), 0 0 30px var(--neon); }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-neon {
  animation: pulse-neon 2s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Utility classes for colors */
.text-foreground { color: var(--foreground); }
.text-accent { color: var(--accent); }
.text-accent-secondary { color: var(--accent-secondary); }
.bg-background { background-color: var(--background); }
.bg-card { background-color: var(--card); }
.bg-muted { background-color: var(--muted); }
.border-glass-border { border-color: var(--glass-border); }

/* Responsive utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
