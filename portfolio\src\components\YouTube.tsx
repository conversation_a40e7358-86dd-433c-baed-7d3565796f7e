'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Play, ExternalLink, Calendar, Eye, ThumbsUp, Youtube } from 'lucide-react';
import Image from 'next/image';

interface YouTubeVideo {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  publishedAt: string;
  viewCount: string;
  likeCount: string;
  duration: string;
  url: string;
}

const YouTube = () => {
  const [videos, setVideos] = useState<YouTubeVideo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data for demonstration - replace with actual API call
  const mockVideos: YouTubeVideo[] = [
    {
      id: '1',
      title: 'Building a Modern React Portfolio with Next.js and Framer Motion',
      description: 'Learn how to create a stunning portfolio website using Next.js, TypeScript, and Framer Motion animations.',
      thumbnail: '/youtube-thumb-1.jpg',
      publishedAt: '2024-01-15',
      viewCount: '12,543',
      likeCount: '892',
      duration: '15:32',
      url: 'https://youtube.com/watch?v=example1'
    },
    {
      id: '2',
      title: 'Advanced CSS Animations and Micro-interactions',
      description: 'Dive deep into CSS animations and learn how to create engaging micro-interactions for better UX.',
      thumbnail: '/youtube-thumb-2.jpg',
      publishedAt: '2024-01-08',
      viewCount: '8,721',
      likeCount: '654',
      duration: '22:18',
      url: 'https://youtube.com/watch?v=example2'
    },
    {
      id: '3',
      title: 'TypeScript Best Practices for React Developers',
      description: 'Essential TypeScript patterns and best practices every React developer should know.',
      thumbnail: '/youtube-thumb-3.jpg',
      publishedAt: '2024-01-01',
      viewCount: '15,892',
      likeCount: '1,234',
      duration: '18:45',
      url: 'https://youtube.com/watch?v=example3'
    },
    {
      id: '4',
      title: 'Creating Responsive Designs with Tailwind CSS',
      description: 'Master responsive design principles using Tailwind CSS utility classes and custom configurations.',
      thumbnail: '/youtube-thumb-4.jpg',
      publishedAt: '2023-12-25',
      viewCount: '9,876',
      likeCount: '743',
      duration: '12:56',
      url: 'https://youtube.com/watch?v=example4'
    },
    {
      id: '5',
      title: 'UI/UX Design Process: From Wireframes to Prototype',
      description: 'Complete walkthrough of my design process from initial wireframes to interactive prototypes.',
      thumbnail: '/youtube-thumb-5.jpg',
      publishedAt: '2023-12-18',
      viewCount: '6,543',
      likeCount: '521',
      duration: '25:12',
      url: 'https://youtube.com/watch?v=example5'
    },
    {
      id: '6',
      title: 'Performance Optimization Techniques for React Apps',
      description: 'Learn advanced techniques to optimize your React applications for better performance and user experience.',
      thumbnail: '/youtube-thumb-6.jpg',
      publishedAt: '2023-12-11',
      viewCount: '11,234',
      likeCount: '876',
      duration: '19:33',
      url: 'https://youtube.com/watch?v=example6'
    }
  ];

  useEffect(() => {
    const fetchVideos = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/youtube-videos');

        if (!response.ok) {
          throw new Error('Failed to fetch videos');
        }

        const data = await response.json();
        setVideos(data);
        setError(null);
      } catch (err) {
        setError('Failed to load videos');
        console.error('Error fetching YouTube videos:', err);
        // Fallback to mock data
        setVideos(mockVideos);
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  if (loading) {
    return (
      <section id="youtube" className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center">
            <div className="animate-spin w-12 h-12 border-4 border-accent border-t-transparent rounded-full mx-auto"></div>
            <p className="mt-4 text-foreground/70">Loading latest videos...</p>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section id="youtube" className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center">
            <p className="text-red-500">{error}</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="youtube" className="py-20 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-accent/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold font-display mb-4">
            <span className="gradient-accent bg-clip-text text-transparent">
              Latest YouTube Videos
            </span>
          </h2>
          <p className="text-lg text-foreground/70 max-w-2xl mx-auto mb-8">
            Sharing knowledge through tutorials, tips, and insights about web development and design
          </p>

          {/* YouTube Channel Link */}
          <motion.a
            href="https://youtube.com/@yourchannel"
            target="_blank"
            rel="noopener noreferrer"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center gap-2 px-6 py-3 bg-red-600 hover:bg-red-700
                     text-white rounded-full font-semibold transition-colors duration-300"
          >
            <Youtube className="w-5 h-5" />
            Subscribe to Channel
          </motion.a>
        </motion.div>

        {/* Videos Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {videos.slice(0, 6).map((video) => (
            <motion.div
              key={video.id}
              variants={itemVariants}
              className="group glass-strong rounded-2xl overflow-hidden hover:scale-105
                       transition-all duration-500 cursor-pointer"
              onClick={() => window.open(video.url, '_blank')}
            >
              {/* Video Thumbnail */}
              <div className="relative aspect-video overflow-hidden">
                <Image
                  src={video.thumbnail}
                  alt={video.title}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-500"
                />

                {/* Play Button Overlay */}
                <div className="absolute inset-0 bg-black/40 flex items-center justify-center
                              opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center"
                  >
                    <Play className="w-6 h-6 text-white ml-1" fill="currentColor" />
                  </motion.div>
                </div>

                {/* Duration Badge */}
                <div className="absolute bottom-2 right-2 px-2 py-1 bg-black/80 text-white
                              text-xs font-semibold rounded">
                  {video.duration}
                </div>
              </div>

              {/* Video Content */}
              <div className="p-6">
                <h3 className="text-lg font-semibold font-display mb-2 line-clamp-2
                             group-hover:text-accent transition-colors duration-300">
                  {video.title}
                </h3>

                <p className="text-foreground/70 text-sm mb-4 line-clamp-3">
                  {video.description}
                </p>

                {/* Video Stats */}
                <div className="flex items-center justify-between text-xs text-foreground/60 mb-4">
                  <div className="flex items-center gap-4">
                    <span className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      {video.viewCount}
                    </span>
                    <span className="flex items-center gap-1">
                      <ThumbsUp className="w-3 h-3" />
                      {video.likeCount}
                    </span>
                  </div>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {formatDate(video.publishedAt)}
                  </span>
                </div>

                {/* Watch Button */}
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full flex items-center justify-center gap-2 py-2 px-4
                           bg-gradient-accent text-white rounded-lg font-medium
                           hover:shadow-lg transition-all duration-300"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(video.url, '_blank');
                  }}
                >
                  <Play className="w-4 h-4" fill="currentColor" />
                  Watch Video
                  <ExternalLink className="w-4 h-4" />
                </motion.button>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* View All Videos Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center mt-12"
        >
          <motion.a
            href="https://youtube.com/@yourchannel/videos"
            target="_blank"
            rel="noopener noreferrer"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center gap-2 px-8 py-4 glass hover:glass-strong
                     rounded-full font-semibold border border-accent/30 hover:border-accent/50
                     transition-all duration-300"
          >
            <Youtube className="w-5 h-5" />
            View All Videos
            <ExternalLink className="w-4 h-4" />
          </motion.a>
        </motion.div>
      </div>
    </section>
  );
};

export default YouTube;
